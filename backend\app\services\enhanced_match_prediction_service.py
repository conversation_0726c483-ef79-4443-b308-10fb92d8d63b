"""
Enhanced Match Prediction Service
Comprehensive prediction system with weather impacts, upset analysis, and half-time predictions
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import logging

from app.services.player_impact_service import PlayerImpactService
from app.services.advanced_prediction_service import AdvancedMLPredictionService
from app.models.prediction import FirstHalfPrediction, EnhancedPredictionResponse

logger = logging.getLogger(__name__)

class EnhancedMatchPredictionService:
    """Comprehensive match prediction with weather, upsets, and half-time analysis"""
    
    def __init__(self, db=None):
        if db is None:
            from app.core.database import NRLDatabase
            db = NRLDatabase()
        self.db = db
        self.player_impact_service = PlayerImpactService(db)
        self.advanced_prediction_service = AdvancedMLPredictionService(db)
        
    def predict_match_comprehensive(
        self, 
        home_team: str, 
        away_team: str, 
        season: int = 2025, 
        round_num: int = 1, 
        venue: str = None,
        weather_conditions: Dict = None
    ) -> Dict:
        """Generate comprehensive match prediction with all enhancements"""
        
        try:
            # Get base prediction from advanced service
            base_prediction = self.advanced_prediction_service.predict_match_advanced(
                home_team, away_team, season, round_num, venue
            )
            
            # Enhance with weather impact analysis
            weather_analysis = self._analyze_weather_impact(
                home_team, away_team, venue, weather_conditions
            )
            
            # Analyze upset possibilities
            upset_analysis = self._analyze_upset_possibilities(
                home_team, away_team, season, round_num, base_prediction
            )
            
            # Generate half-time predictions with consistency
            halftime_prediction = self._predict_halftime_comprehensive(
                home_team, away_team, season, round_num, venue, 
                base_prediction, weather_analysis, upset_analysis
            )
            
            # Apply weather adjustments to full-time prediction
            adjusted_prediction = self._apply_weather_adjustments(
                base_prediction, weather_analysis, upset_analysis
            )
            
            # Ensure consistency between half-time and full-time
            consistent_predictions = self._ensure_prediction_consistency(
                adjusted_prediction, halftime_prediction
            )
            
            # Generate comprehensive analysis
            comprehensive_analysis = self._generate_comprehensive_analysis(
                home_team, away_team, season, round_num, venue,
                consistent_predictions, weather_analysis, upset_analysis
            )
            
            return {
                **consistent_predictions['fulltime'],
                'halftime_prediction': consistent_predictions['halftime'],
                'weather_analysis': weather_analysis,
                'upset_analysis': upset_analysis,
                'comprehensive_analysis': comprehensive_analysis,
                'model_version': 'enhanced_comprehensive_v1.0'
            }
            
        except Exception as e:
            logger.error(f"Comprehensive prediction failed: {e}")
            return self._fallback_comprehensive_prediction(home_team, away_team)
    
    def _analyze_weather_impact(
        self, 
        home_team: str, 
        away_team: str, 
        venue: str, 
        weather_conditions: Dict = None
    ) -> Dict:
        """Comprehensive weather impact analysis"""
        try:
            # Get weather conditions (real or estimated)
            if not weather_conditions:
                weather_conditions = self._estimate_weather_conditions(venue)
            
            # Team-specific weather performance profiles
            weather_profiles = self._get_weather_performance_profiles()
            
            home_profile = weather_profiles.get(home_team, weather_profiles['default'])
            away_profile = weather_profiles.get(away_team, weather_profiles['default'])
            
            # Analyze different weather factors
            temperature_impact = self._analyze_temperature_impact(
                weather_conditions, home_profile, away_profile
            )
            
            rain_impact = self._analyze_rain_impact(
                weather_conditions, home_profile, away_profile
            )
            
            wind_impact = self._analyze_wind_impact(
                weather_conditions, home_profile, away_profile, venue
            )
            
            # Combined weather impact
            home_weather_factor = (
                temperature_impact['home_factor'] * 0.3 +
                rain_impact['home_factor'] * 0.5 +
                wind_impact['home_factor'] * 0.2
            )
            
            away_weather_factor = (
                temperature_impact['away_factor'] * 0.3 +
                rain_impact['away_factor'] * 0.5 +
                wind_impact['away_factor'] * 0.2
            )
            
            return {
                'weather_conditions': weather_conditions,
                'home_weather_factor': home_weather_factor,
                'away_weather_factor': away_weather_factor,
                'temperature_analysis': temperature_impact,
                'rain_analysis': rain_impact,
                'wind_analysis': wind_impact,
                'overall_impact': self._categorize_weather_impact(home_weather_factor, away_weather_factor),
                'betting_implications': self._generate_weather_betting_insights(
                    home_weather_factor, away_weather_factor, weather_conditions
                )
            }
            
        except Exception as e:
            logger.error(f"Weather analysis failed: {e}")
            return self._default_weather_analysis()
    
    def _analyze_upset_possibilities(
        self, 
        home_team: str, 
        away_team: str, 
        season: int, 
        round_num: int, 
        base_prediction: Dict
    ) -> Dict:
        """Analyze potential for upsets based on multiple factors"""
        try:
            # Get team form and momentum
            home_form = self._get_team_recent_form(home_team, season, round_num)
            away_form = self._get_team_recent_form(away_team, season, round_num)
            
            # Analyze historical head-to-head upsets
            h2h_upset_history = self._analyze_h2h_upset_history(home_team, away_team)
            
            # Check for upset-prone scenarios
            upset_factors = self._identify_upset_factors(
                home_team, away_team, season, round_num, base_prediction
            )
            
            # Calculate upset probability
            upset_probability = self._calculate_upset_probability(
                home_form, away_form, h2h_upset_history, upset_factors, base_prediction
            )
            
            # Determine upset scenarios
            upset_scenarios = self._generate_upset_scenarios(
                home_team, away_team, upset_probability, upset_factors
            )
            
            return {
                'upset_probability': upset_probability,
                'home_form_trend': home_form,
                'away_form_trend': away_form,
                'h2h_upset_history': h2h_upset_history,
                'upset_factors': upset_factors,
                'upset_scenarios': upset_scenarios,
                'confidence_adjustment': self._calculate_confidence_adjustment(upset_probability),
                'betting_implications': self._generate_upset_betting_insights(upset_probability, upset_scenarios)
            }
            
        except Exception as e:
            logger.error(f"Upset analysis failed: {e}")
            return self._default_upset_analysis()
    
    def _predict_halftime_comprehensive(
        self, 
        home_team: str, 
        away_team: str, 
        season: int, 
        round_num: int, 
        venue: str,
        base_prediction: Dict,
        weather_analysis: Dict,
        upset_analysis: Dict
    ) -> Dict:
        """Comprehensive half-time prediction with all factors"""
        try:
            # Get team-specific half-time patterns
            home_ht_patterns = self._get_halftime_patterns(home_team, season)
            away_ht_patterns = self._get_halftime_patterns(away_team, season)
            
            # Apply TMP (Team, Match, Player) factors to half-time
            tmp_factors = self._apply_tmp_factors_halftime(
                home_team, away_team, season, round_num, venue
            )
            
            # Base half-time scoring (typically 45-55% of full game)
            base_ht_ratio = 0.5
            
            # Adjust ratio based on team patterns
            home_ht_ratio = home_ht_patterns.get('scoring_ratio', base_ht_ratio)
            away_ht_ratio = away_ht_patterns.get('scoring_ratio', base_ht_ratio)
            combined_ht_ratio = (home_ht_ratio + away_ht_ratio) / 2
            
            # Apply weather impact to half-time
            weather_ht_adjustment = self._calculate_halftime_weather_impact(weather_analysis)
            
            # Apply upset factors to half-time
            upset_ht_adjustment = self._calculate_halftime_upset_impact(upset_analysis)
            
            # Calculate half-time scores (ensure even numbers)
            predicted_ht_home_float = (
                base_prediction['predicted_home_score'] * combined_ht_ratio *
                weather_ht_adjustment * upset_ht_adjustment * tmp_factors['home_factor']
            )
            predicted_ht_away_float = (
                base_prediction['predicted_away_score'] * combined_ht_ratio *
                weather_ht_adjustment * upset_ht_adjustment * tmp_factors['away_factor']
            )

            # Round to even numbers
            predicted_ht_home = (round(predicted_ht_home_float) // 2) * 2
            predicted_ht_away = (round(predicted_ht_away_float) // 2) * 2

            # Ensure realistic half-time scores (even numbers)
            predicted_ht_home = max(0, min(predicted_ht_home, base_prediction['predicted_home_score'] - 2))
            predicted_ht_away = max(0, min(predicted_ht_away, base_prediction['predicted_away_score'] - 2))

            # Final even number check
            predicted_ht_home = (predicted_ht_home // 2) * 2
            predicted_ht_away = (predicted_ht_away // 2) * 2
            
            # Half-time winner and margin
            ht_winner = home_team if predicted_ht_home > predicted_ht_away else away_team
            ht_margin = abs(predicted_ht_home - predicted_ht_away)
            ht_total = predicted_ht_home + predicted_ht_away
            
            # First score prediction
            first_score_analysis = self._predict_first_score(
                home_team, away_team, tmp_factors, weather_analysis
            )
            
            return {
                'ht_home_score': predicted_ht_home,
                'ht_away_score': predicted_ht_away,
                'ht_winner': ht_winner,
                'ht_margin': ht_margin,
                'ht_total_points': ht_total,
                'ht_confidence': self._calculate_halftime_confidence(
                    home_ht_patterns, away_ht_patterns, weather_analysis, upset_analysis
                ),
                'first_score_analysis': first_score_analysis,
                'tmp_factors': tmp_factors,
                'halftime_patterns': {
                    'home_patterns': home_ht_patterns,
                    'away_patterns': away_ht_patterns
                }
            }
            
        except Exception as e:
            logger.error(f"Half-time prediction failed: {e}")
            return self._default_halftime_prediction(base_prediction)

    def _get_weather_performance_profiles(self) -> Dict:
        """Get team-specific weather performance profiles"""
        return {
            # Wet weather specialists
            'Melbourne Storm': {
                'rain_factor': 1.08, 'cold_factor': 1.05, 'wind_factor': 1.03,
                'strengths': ['forward_dominance', 'structured_play', 'wet_weather_experience']
            },
            'Penrith Panthers': {
                'rain_factor': 1.05, 'cold_factor': 1.03, 'wind_factor': 1.02,
                'strengths': ['forward_pack', 'power_game', 'adaptability']
            },
            'Canterbury Bulldogs': {
                'rain_factor': 1.04, 'cold_factor': 1.02, 'wind_factor': 1.01,
                'strengths': ['forward_oriented', 'defensive_structure']
            },
            'St George Illawarra Dragons': {
                'rain_factor': 1.03, 'cold_factor': 1.01, 'wind_factor': 1.00,
                'strengths': ['traditional_style', 'forward_dominance']
            },

            # Dry weather/skill teams (perform worse in wet)
            'Sydney Roosters': {
                'rain_factor': 0.96, 'cold_factor': 0.98, 'wind_factor': 0.97,
                'strengths': ['skill_based', 'fast_play', 'attacking_flair']
            },
            'Manly Sea Eagles': {
                'rain_factor': 0.97, 'cold_factor': 0.98, 'wind_factor': 0.96,
                'strengths': ['skill_based', 'attacking_flair', 'individual_brilliance']
            },
            'Parramatta Eels': {
                'rain_factor': 0.95, 'cold_factor': 0.97, 'wind_factor': 0.96,
                'strengths': ['high_skill', 'fast_play', 'attacking_structure']
            },
            'Cronulla Sharks': {
                'rain_factor': 0.98, 'cold_factor': 0.99, 'wind_factor': 0.97,
                'strengths': ['skill_based', 'coastal_conditions']
            },

            # Balanced teams
            'Brisbane Broncos': {
                'rain_factor': 1.00, 'cold_factor': 0.98, 'wind_factor': 0.99,
                'strengths': ['young_adaptability', 'mixed_style']
            },
            'South Sydney Rabbitohs': {
                'rain_factor': 1.02, 'cold_factor': 1.00, 'wind_factor': 1.00,
                'strengths': ['versatile_attack', 'experience']
            },

            # Default profile
            'default': {
                'rain_factor': 1.00, 'cold_factor': 1.00, 'wind_factor': 1.00,
                'strengths': ['standard_nrl_team']
            }
        }

    def _analyze_temperature_impact(self, weather: Dict, home_profile: Dict, away_profile: Dict) -> Dict:
        """Analyze temperature impact on teams"""
        try:
            temp = weather.get('temperature', 20)

            # Temperature impact factors
            if temp < 10:  # Very cold
                impact_severity = 'High'
                base_factor = 0.95
            elif temp < 15:  # Cold
                impact_severity = 'Medium'
                base_factor = 0.98
            elif temp > 30:  # Hot
                impact_severity = 'Medium'
                base_factor = 0.97
            elif temp > 35:  # Very hot
                impact_severity = 'High'
                base_factor = 0.94
            else:  # Ideal conditions
                impact_severity = 'Low'
                base_factor = 1.00

            home_factor = base_factor * home_profile.get('cold_factor', 1.0) if temp < 15 else base_factor
            away_factor = base_factor * away_profile.get('cold_factor', 1.0) if temp < 15 else base_factor

            return {
                'temperature': temp,
                'impact_severity': impact_severity,
                'home_factor': home_factor,
                'away_factor': away_factor,
                'analysis': f"Temperature {temp}°C - {impact_severity} impact on performance"
            }

        except Exception as e:
            logger.error(f"Temperature analysis failed: {e}")
            return {'home_factor': 1.0, 'away_factor': 1.0, 'analysis': 'Temperature analysis failed'}

    def _analyze_rain_impact(self, weather: Dict, home_profile: Dict, away_profile: Dict) -> Dict:
        """Analyze rain impact on teams"""
        try:
            precipitation = weather.get('precipitation', 0)
            weather_desc = weather.get('weather_description', '').lower()

            # Determine rain severity
            if precipitation > 5 or 'heavy rain' in weather_desc:
                rain_severity = 'Heavy'
                base_impact = 0.90
            elif precipitation > 1 or 'rain' in weather_desc or 'shower' in weather_desc:
                rain_severity = 'Moderate'
                base_impact = 0.95
            elif 'drizzle' in weather_desc or precipitation > 0:
                rain_severity = 'Light'
                base_impact = 0.98
            else:
                rain_severity = 'None'
                base_impact = 1.00

            # Apply team-specific rain factors
            home_factor = base_impact * home_profile.get('rain_factor', 1.0)
            away_factor = base_impact * away_profile.get('rain_factor', 1.0)

            return {
                'precipitation': precipitation,
                'rain_severity': rain_severity,
                'home_factor': home_factor,
                'away_factor': away_factor,
                'analysis': f"{rain_severity} rain conditions - affects skill-based teams more",
                'tactical_impact': 'Favors forward-oriented teams and defensive play'
            }

        except Exception as e:
            logger.error(f"Rain analysis failed: {e}")
            return {'home_factor': 1.0, 'away_factor': 1.0, 'analysis': 'Rain analysis failed'}

    def _analyze_wind_impact(self, weather: Dict, home_profile: Dict, away_profile: Dict, venue: str) -> Dict:
        """Analyze wind impact on teams and kicking game"""
        try:
            wind_speed = weather.get('wind_speed', 0)
            wind_direction = weather.get('wind_direction', 0)

            # Wind impact on kicking and passing
            if wind_speed > 25:  # Strong wind
                wind_severity = 'Strong'
                kicking_impact = 0.85
                passing_impact = 0.92
            elif wind_speed > 15:  # Moderate wind
                wind_severity = 'Moderate'
                kicking_impact = 0.92
                passing_impact = 0.96
            elif wind_speed > 8:  # Light wind
                wind_severity = 'Light'
                kicking_impact = 0.96
                passing_impact = 0.98
            else:  # Calm
                wind_severity = 'Calm'
                kicking_impact = 1.00
                passing_impact = 1.00

            # Venue-specific wind effects
            windy_venues = ['ANZ Stadium', 'Accor Stadium', 'McDonald Jones Stadium']
            if venue in windy_venues:
                kicking_impact *= 0.95
                passing_impact *= 0.97

            # Team-specific wind adaptation
            home_factor = (kicking_impact + passing_impact) / 2 * home_profile.get('wind_factor', 1.0)
            away_factor = (kicking_impact + passing_impact) / 2 * away_profile.get('wind_factor', 1.0)

            return {
                'wind_speed': wind_speed,
                'wind_severity': wind_severity,
                'kicking_impact': kicking_impact,
                'passing_impact': passing_impact,
                'home_factor': home_factor,
                'away_factor': away_factor,
                'analysis': f"{wind_severity} wind ({wind_speed} km/h) - affects kicking and passing accuracy"
            }

        except Exception as e:
            logger.error(f"Wind analysis failed: {e}")
            return {'home_factor': 1.0, 'away_factor': 1.0, 'analysis': 'Wind analysis failed'}

    def _get_team_recent_form(self, team: str, season: int, round_num: int) -> Dict:
        """Get team's recent form and momentum for upset analysis"""
        try:
            # Get last 5 matches
            matches_df = self.db.get_matches_df(season=season, team=team)
            if matches_df.empty:
                return {'form_rating': 0.5, 'momentum': 'neutral', 'trend': 'stable'}

            recent_matches = matches_df[
                (matches_df['round'] < round_num) &
                (matches_df['home_score'].notna())
            ].tail(5)

            if recent_matches.empty:
                return {'form_rating': 0.5, 'momentum': 'neutral', 'trend': 'stable'}

            # Calculate wins and performance
            wins = 0
            points_for = []
            points_against = []
            margins = []

            for _, match in recent_matches.iterrows():
                if match['home_team'] == team:
                    team_score = match['home_score']
                    opp_score = match['away_score']
                else:
                    team_score = match['away_score']
                    opp_score = match['home_score']

                if team_score > opp_score:
                    wins += 1

                points_for.append(team_score)
                points_against.append(opp_score)
                margins.append(team_score - opp_score)

            form_rating = wins / len(recent_matches)
            avg_margin = np.mean(margins)

            # Determine momentum
            if form_rating >= 0.8:
                momentum = 'hot'
            elif form_rating >= 0.6:
                momentum = 'good'
            elif form_rating >= 0.4:
                momentum = 'average'
            elif form_rating >= 0.2:
                momentum = 'poor'
            else:
                momentum = 'cold'

            # Determine trend (last 3 vs first 2 games)
            if len(recent_matches) >= 5:
                early_wins = sum(1 for _, match in recent_matches.head(2).iterrows()
                               if self._is_team_winner(match, team))
                late_wins = sum(1 for _, match in recent_matches.tail(3).iterrows()
                              if self._is_team_winner(match, team))

                early_rate = early_wins / 2
                late_rate = late_wins / 3

                if late_rate > early_rate + 0.2:
                    trend = 'improving'
                elif late_rate < early_rate - 0.2:
                    trend = 'declining'
                else:
                    trend = 'stable'
            else:
                trend = 'stable'

            return {
                'form_rating': form_rating,
                'momentum': momentum,
                'trend': trend,
                'avg_margin': avg_margin,
                'recent_wins': wins,
                'recent_games': len(recent_matches)
            }

        except Exception as e:
            logger.error(f"Form analysis failed for {team}: {e}")
            return {'form_rating': 0.5, 'momentum': 'neutral', 'trend': 'stable'}

    def _identify_upset_factors(self, home_team: str, away_team: str, season: int,
                               round_num: int, base_prediction: Dict) -> List[Dict]:
        """Identify factors that could lead to upsets"""
        upset_factors = []

        try:
            confidence = base_prediction.get('confidence', 60)
            predicted_winner = base_prediction.get('predicted_winner', home_team)
            margin = base_prediction.get('predicted_margin', 6)

            # Factor 1: Low confidence prediction
            if confidence < 65:
                upset_factors.append({
                    'factor': 'low_confidence',
                    'description': f'Low prediction confidence ({confidence}%)',
                    'upset_boost': 0.15,
                    'severity': 'medium'
                })

            # Factor 2: Close predicted margin
            if margin <= 6:
                upset_factors.append({
                    'factor': 'close_margin',
                    'description': f'Close predicted margin ({margin} points)',
                    'upset_boost': 0.20,
                    'severity': 'high'
                })

            # Factor 3: Origin period uncertainty
            origin_rounds = [14, 15, 17]
            if round_num in origin_rounds:
                upset_factors.append({
                    'factor': 'origin_period',
                    'description': 'State of Origin period - key players unavailable',
                    'upset_boost': 0.25,
                    'severity': 'high'
                })

            # Factor 4: Derby matches (traditional rivalries)
            derby_pairs = [
                ('Sydney Roosters', 'South Sydney Rabbitohs'),
                ('Parramatta Eels', 'Canterbury Bulldogs'),
                ('Manly Sea Eagles', 'Cronulla Sharks'),
                ('St George Illawarra Dragons', 'Cronulla Sharks')
            ]

            is_derby = any((home_team in pair and away_team in pair) for pair in derby_pairs)
            if is_derby:
                upset_factors.append({
                    'factor': 'derby_match',
                    'description': 'Traditional rivalry - form often goes out the window',
                    'upset_boost': 0.18,
                    'severity': 'medium'
                })

            # Factor 5: Late season pressure
            if round_num >= 20:
                upset_factors.append({
                    'factor': 'finals_pressure',
                    'description': 'Late season - finals pressure affects performance',
                    'upset_boost': 0.12,
                    'severity': 'medium'
                })

            return upset_factors

        except Exception as e:
            logger.error(f"Upset factor identification failed: {e}")
            return []

    def _calculate_upset_probability(self, home_form: Dict, away_form: Dict,
                                   h2h_history: Dict, upset_factors: List,
                                   base_prediction: Dict) -> Dict:
        """Calculate overall upset probability"""
        try:
            base_confidence = base_prediction.get('confidence', 60) / 100
            predicted_winner = base_prediction.get('predicted_winner', '')

            # Base upset probability (inverse of confidence)
            base_upset_prob = 1 - base_confidence

            # Form-based adjustments
            home_form_rating = home_form.get('form_rating', 0.5)
            away_form_rating = away_form.get('form_rating', 0.5)

            # If underdog has better recent form, increase upset chance
            if predicted_winner and 'home' in predicted_winner.lower():
                underdog_form = away_form_rating
                favorite_form = home_form_rating
            else:
                underdog_form = home_form_rating
                favorite_form = away_form_rating

            form_adjustment = (underdog_form - favorite_form) * 0.3

            # Upset factors boost
            factor_boost = sum(factor.get('upset_boost', 0) for factor in upset_factors)

            # Historical upset tendency
            h2h_upset_rate = h2h_history.get('upset_rate', 0.2)

            # Combined upset probability
            final_upset_prob = min(0.45, max(0.05,
                base_upset_prob + form_adjustment + factor_boost + (h2h_upset_rate - 0.2) * 0.5
            ))

            # Categorize upset likelihood
            if final_upset_prob > 0.35:
                likelihood = 'High'
            elif final_upset_prob > 0.25:
                likelihood = 'Medium'
            elif final_upset_prob > 0.15:
                likelihood = 'Low'
            else:
                likelihood = 'Very Low'

            return {
                'upset_probability': final_upset_prob,
                'likelihood_category': likelihood,
                'base_probability': base_upset_prob,
                'form_adjustment': form_adjustment,
                'factor_boost': factor_boost,
                'h2h_influence': h2h_upset_rate
            }

        except Exception as e:
            logger.error(f"Upset probability calculation failed: {e}")
            return {'upset_probability': 0.2, 'likelihood_category': 'Low'}

    def _apply_tmp_factors_halftime(self, home_team: str, away_team: str, season: int,
                                   round_num: int, venue: str) -> Dict:
        """Apply Team, Match, Player factors specifically for half-time predictions"""
        try:
            # Team factors (T)
            team_factors = self.player_impact_service.calculate_combined_impact(
                home_team, away_team, season, round_num
            )

            # Match factors (M) - venue, conditions, context
            match_factors = self._calculate_match_context_factors(venue, round_num, season)

            # Player factors (P) - already included in team_factors from player_impact_service

            # Half-time specific adjustments
            ht_adjustments = self._get_halftime_specific_adjustments(home_team, away_team)

            # Combine factors for half-time
            home_factor = (
                team_factors['home_team_impact']['combined_scoring_factor'] * 0.6 +
                match_factors['home_match_factor'] * 0.3 +
                ht_adjustments['home_ht_factor'] * 0.1
            )

            away_factor = (
                team_factors['away_team_impact']['combined_scoring_factor'] * 0.6 +
                match_factors['away_match_factor'] * 0.3 +
                ht_adjustments['away_ht_factor'] * 0.1
            )

            return {
                'home_factor': home_factor,
                'away_factor': away_factor,
                'team_factors': team_factors,
                'match_factors': match_factors,
                'halftime_adjustments': ht_adjustments
            }

        except Exception as e:
            logger.error(f"TMP factors calculation failed: {e}")
            return {'home_factor': 1.0, 'away_factor': 1.0}

    def _ensure_prediction_consistency(self, fulltime_prediction: Dict, halftime_prediction: Dict) -> Dict:
        """Ensure consistency between half-time and full-time predictions"""
        try:
            ft_home = fulltime_prediction['predicted_home_score']
            ft_away = fulltime_prediction['predicted_away_score']
            ht_home = halftime_prediction['ht_home_score']
            ht_away = halftime_prediction['ht_away_score']

            # Check for logical consistency
            adjustments_made = []

            # Rule 1: Half-time scores cannot exceed full-time scores (ensure even numbers)
            if ht_home > ft_home:
                ht_home = max(0, ft_home - 2)
                ht_home = (ht_home // 2) * 2  # Ensure even
                adjustments_made.append("Reduced HT home score to maintain consistency")

            if ht_away > ft_away:
                ht_away = max(0, ft_away - 2)
                ht_away = (ht_away // 2) * 2  # Ensure even
                adjustments_made.append("Reduced HT away score to maintain consistency")

            # Rule 2: Half-time winner should generally align with full-time winner (but allow for comebacks)
            ft_winner = fulltime_prediction['predicted_winner']
            ht_winner = halftime_prediction['ht_winner']

            # Rule 3: Ensure realistic second-half scoring
            second_half_home = ft_home - ht_home
            second_half_away = ft_away - ht_away

            # Minimum second-half scoring (teams rarely score 0 in second half, ensure even)
            if second_half_home < 2 and ft_home > ht_home:
                ht_home = max(0, ft_home - 4)
                ht_home = (ht_home // 2) * 2  # Ensure even
                adjustments_made.append("Adjusted HT home to ensure realistic 2nd half scoring")

            if second_half_away < 2 and ft_away > ht_away:
                ht_away = max(0, ft_away - 4)
                ht_away = (ht_away // 2) * 2  # Ensure even
                adjustments_made.append("Adjusted HT away to ensure realistic 2nd half scoring")

            # Rule 4: Check for extreme second-half comebacks (flag as low confidence)
            ht_margin = abs(ht_home - ht_away)
            ft_margin = abs(ft_home - ft_away)

            comeback_factor = 1.0
            if ht_winner != ft_winner and ht_margin > 6:
                comeback_factor = 0.85  # Reduce confidence for big comebacks
                adjustments_made.append("Large comeback predicted - reduced confidence")

            # Recalculate derived values
            ht_total = ht_home + ht_away
            ht_margin_final = abs(ht_home - ht_away)
            ht_winner_final = halftime_prediction['ht_winner']

            # Update confidence based on consistency
            base_confidence = fulltime_prediction.get('confidence', 60)
            adjusted_confidence = base_confidence * comeback_factor

            return {
                'fulltime': {
                    **fulltime_prediction,
                    'confidence': adjusted_confidence
                },
                'halftime': {
                    **halftime_prediction,
                    'ht_home_score': ht_home,
                    'ht_away_score': ht_away,
                    'ht_total_points': ht_total,
                    'ht_margin': ht_margin_final,
                    'ht_winner': ht_winner_final
                },
                'consistency_check': {
                    'adjustments_made': adjustments_made,
                    'comeback_factor': comeback_factor,
                    'second_half_home': ft_home - ht_home,
                    'second_half_away': ft_away - ht_away
                }
            }

        except Exception as e:
            logger.error(f"Consistency check failed: {e}")
            return {'fulltime': fulltime_prediction, 'halftime': halftime_prediction}

    def _predict_first_score(self, home_team: str, away_team: str, tmp_factors: Dict,
                           weather_analysis: Dict) -> Dict:
        """Predict which team scores first and how"""
        try:
            # Base first score probabilities (home advantage)
            home_first_prob = 0.55

            # Adjust based on team factors
            team_strength_diff = tmp_factors['home_factor'] - tmp_factors['away_factor']
            home_first_prob += team_strength_diff * 0.2

            # Weather impact on first score
            weather_impact = (weather_analysis['home_weather_factor'] - weather_analysis['away_weather_factor']) * 0.1
            home_first_prob += weather_impact

            # Clamp probability
            home_first_prob = max(0.3, min(0.7, home_first_prob))

            first_score_team = home_team if home_first_prob > 0.5 else away_team
            first_score_confidence = max(home_first_prob, 1 - home_first_prob)

            # Predict first score type based on weather and team style
            if weather_analysis.get('rain_analysis', {}).get('rain_severity') in ['Heavy', 'Moderate']:
                first_score_type = 'Penalty Goal'
                time_range = '0-15 min'
            else:
                first_score_type = 'Try'
                time_range = '0-20 min'

            return {
                'first_score_team': first_score_team,
                'first_score_confidence': first_score_confidence,
                'first_score_type': first_score_type,
                'first_score_time_range': time_range,
                'home_first_probability': home_first_prob,
                'away_first_probability': 1 - home_first_prob
            }

        except Exception as e:
            logger.error(f"First score prediction failed: {e}")
            return {
                'first_score_team': home_team,
                'first_score_confidence': 0.55,
                'first_score_type': 'Try',
                'first_score_time_range': '0-20 min'
            }

    # Helper methods
    def _is_team_winner(self, match: pd.Series, team: str) -> bool:
        """Check if team won the match"""
        if match['home_team'] == team:
            return match['home_score'] > match['away_score']
        else:
            return match['away_score'] > match['home_score']

    def _estimate_weather_conditions(self, venue: str) -> Dict:
        """Estimate weather conditions if not provided"""
        # Simplified weather estimation based on venue and season
        return {
            'temperature': 20,
            'precipitation': 0,
            'wind_speed': 10,
            'weather_description': 'partly cloudy',
            'estimated': True
        }

    def _categorize_weather_impact(self, home_factor: float, away_factor: float) -> str:
        """Categorize overall weather impact"""
        avg_impact = (home_factor + away_factor) / 2
        if avg_impact < 0.95:
            return 'High Negative Impact'
        elif avg_impact < 0.98:
            return 'Moderate Negative Impact'
        elif avg_impact > 1.02:
            return 'Positive Impact'
        else:
            return 'Minimal Impact'

    def _generate_weather_betting_insights(self, home_factor: float, away_factor: float,
                                         weather: Dict) -> List[str]:
        """Generate weather-based betting insights"""
        insights = []

        if weather.get('precipitation', 0) > 1:
            insights.append("Consider UNDER total points - wet conditions reduce scoring")
            insights.append("Favor forward-oriented teams in wet weather")

        if weather.get('wind_speed', 0) > 20:
            insights.append("Kicking accuracy reduced - consider alternative scoring methods")

        if abs(home_factor - away_factor) > 0.05:
            better_team = "Home" if home_factor > away_factor else "Away"
            insights.append(f"{better_team} team better adapted to weather conditions")

        return insights

    def _analyze_h2h_upset_history(self, home_team: str, away_team: str) -> Dict:
        """Analyze historical upsets between teams"""
        try:
            # Get historical matches between teams
            matches_df = self.db.get_matches_df()
            if matches_df.empty:
                return {'upset_rate': 0.2, 'total_matches': 0}

            h2h_matches = matches_df[
                ((matches_df['home_team'] == home_team) & (matches_df['away_team'] == away_team)) |
                ((matches_df['home_team'] == away_team) & (matches_df['away_team'] == home_team))
            ]

            if len(h2h_matches) < 3:
                return {'upset_rate': 0.2, 'total_matches': len(h2h_matches)}

            # Simple upset detection (could be enhanced with betting odds data)
            upsets = 0
            for _, match in h2h_matches.iterrows():
                # Basic upset: away team wins by significant margin
                if match['away_score'] > match['home_score'] + 6:
                    upsets += 1

            upset_rate = upsets / len(h2h_matches)

            return {
                'upset_rate': upset_rate,
                'total_matches': len(h2h_matches),
                'historical_upsets': upsets
            }

        except Exception as e:
            logger.error(f"H2H upset analysis failed: {e}")
            return {'upset_rate': 0.2, 'total_matches': 0}

    def _generate_upset_scenarios(self, home_team: str, away_team: str,
                                upset_prob: Dict, upset_factors: List) -> List[Dict]:
        """Generate specific upset scenarios"""
        scenarios = []

        if upset_prob['upset_probability'] > 0.25:
            scenarios.append({
                'scenario': 'Form Reversal',
                'description': 'Underdog team in better recent form causes upset',
                'probability': min(0.35, upset_prob['upset_probability'] + 0.1),
                'key_factors': [f['factor'] for f in upset_factors if f['severity'] == 'high']
            })

        if upset_prob['upset_probability'] > 0.2:
            scenarios.append({
                'scenario': 'Close Contest',
                'description': 'Tight match decided by small margins or key moments',
                'probability': upset_prob['upset_probability'],
                'key_factors': ['close_margin', 'low_confidence']
            })

        return scenarios

    def _calculate_match_context_factors(self, venue: str, round_num: int, season: int) -> Dict:
        """Calculate match context factors"""
        try:
            # Venue factors
            home_advantage = 1.05  # Base home advantage

            # Round context
            if round_num <= 3:
                context = 'early_season'
                uncertainty_factor = 1.1
            elif round_num >= 20:
                context = 'finals_race'
                pressure_factor = 1.05
                uncertainty_factor = 1.0
            else:
                context = 'regular_season'
                uncertainty_factor = 1.0
                pressure_factor = 1.0

            return {
                'home_match_factor': home_advantage,
                'away_match_factor': 1.0,
                'context': context,
                'uncertainty_factor': uncertainty_factor,
                'pressure_factor': getattr(locals(), 'pressure_factor', 1.0)
            }

        except Exception as e:
            logger.error(f"Match context calculation failed: {e}")
            return {'home_match_factor': 1.05, 'away_match_factor': 1.0}

    def _get_halftime_specific_adjustments(self, home_team: str, away_team: str) -> Dict:
        """Get team-specific half-time adjustments"""
        # Teams that start fast vs slow starters
        fast_starters = ['Sydney Roosters', 'Melbourne Storm', 'Penrith Panthers']
        slow_starters = ['Brisbane Broncos', 'Wests Tigers', 'Gold Coast Titans']

        home_ht_factor = 1.0
        away_ht_factor = 1.0

        if home_team in fast_starters:
            home_ht_factor = 1.05
        elif home_team in slow_starters:
            home_ht_factor = 0.95

        if away_team in fast_starters:
            away_ht_factor = 1.05
        elif away_team in slow_starters:
            away_ht_factor = 0.95

        return {
            'home_ht_factor': home_ht_factor,
            'away_ht_factor': away_ht_factor
        }

    def _get_halftime_patterns(self, team: str, season: int) -> Dict:
        """Get team's historical half-time patterns"""
        try:
            # Get team's matches with half-time data (if available)
            # For now, use estimated patterns based on team style

            team_patterns = {
                'Sydney Roosters': {'scoring_ratio': 0.55, 'consistency': 0.8},
                'Melbourne Storm': {'scoring_ratio': 0.52, 'consistency': 0.85},
                'Penrith Panthers': {'scoring_ratio': 0.54, 'consistency': 0.82},
                'Brisbane Broncos': {'scoring_ratio': 0.48, 'consistency': 0.65},
                'Wests Tigers': {'scoring_ratio': 0.47, 'consistency': 0.60}
            }

            return team_patterns.get(team, {'scoring_ratio': 0.50, 'consistency': 0.70})

        except Exception as e:
            logger.error(f"Half-time patterns analysis failed for {team}: {e}")
            return {'scoring_ratio': 0.50, 'consistency': 0.70}

    # Default/fallback methods
    def _default_weather_analysis(self) -> Dict:
        """Default weather analysis when calculation fails"""
        return {
            'home_weather_factor': 1.0,
            'away_weather_factor': 1.0,
            'overall_impact': 'Minimal Impact',
            'betting_implications': ['Standard weather conditions assumed']
        }

    def _default_upset_analysis(self) -> Dict:
        """Default upset analysis when calculation fails"""
        return {
            'upset_probability': 0.2,
            'likelihood_category': 'Low',
            'upset_factors': [],
            'confidence_adjustment': 1.0,
            'betting_implications': ['Standard upset risk']
        }

    def _default_halftime_prediction(self, base_prediction: Dict) -> Dict:
        """Default half-time prediction when calculation fails"""
        # Calculate even half-time scores
        ht_home_float = base_prediction.get('predicted_home_score', 20) * 0.5
        ht_away_float = base_prediction.get('predicted_away_score', 18) * 0.5

        ht_home = (round(ht_home_float) // 2) * 2
        ht_away = (round(ht_away_float) // 2) * 2

        return {
            'ht_home_score': ht_home,
            'ht_away_score': ht_away,
            'ht_winner': base_prediction.get('predicted_winner', 'Unknown'),
            'ht_margin': abs(ht_home - ht_away),
            'ht_total_points': ht_home + ht_away,
            'ht_confidence': 60.0
        }

    def _fallback_comprehensive_prediction(self, home_team: str, away_team: str) -> Dict:
        """Fallback prediction when comprehensive analysis fails"""
        # Ensure even scores in fallback
        home_score = 22  # Already even
        away_score = 18  # Already even

        return {
            'predicted_winner': home_team,
            'confidence': 55.0,
            'predicted_home_score': home_score,
            'predicted_away_score': away_score,
            'predicted_margin': abs(home_score - away_score),
            'predicted_total_points': home_score + away_score,
            'halftime_prediction': self._default_halftime_prediction({
                'predicted_home_score': home_score, 'predicted_away_score': away_score, 'predicted_winner': home_team
            }),
            'weather_analysis': self._default_weather_analysis(),
            'upset_analysis': self._default_upset_analysis(),
            'model_version': 'fallback_v1.0'
        }

    def _generate_comprehensive_analysis(self, home_team: str, away_team: str, season: int,
                                       round_num: int, venue: str, predictions: Dict,
                                       weather_analysis: Dict, upset_analysis: Dict) -> Dict:
        """Generate comprehensive match analysis"""
        try:
            # Key insights
            insights = []

            # Weather insights
            weather_impact = weather_analysis.get('overall_impact', 'Minimal Impact')
            if weather_impact != 'Minimal Impact':
                insights.append(f"Weather conditions: {weather_impact}")

            # Upset insights
            upset_likelihood = upset_analysis.get('likelihood_category', 'Low')
            if upset_likelihood in ['High', 'Medium']:
                insights.append(f"Upset potential: {upset_likelihood}")

            # Half-time vs full-time consistency
            consistency = predictions.get('consistency_check', {})
            if consistency.get('adjustments_made'):
                insights.append("Prediction adjustments made for consistency")

            # Betting recommendations
            betting_recs = []
            betting_recs.extend(weather_analysis.get('betting_implications', []))
            betting_recs.extend(upset_analysis.get('betting_implications', []))

            # Confidence factors
            confidence_factors = {
                'base_prediction': predictions['fulltime'].get('confidence', 60),
                'weather_impact': weather_analysis.get('confidence_impact', 1.0),
                'upset_adjustment': upset_analysis.get('confidence_adjustment', 1.0),
                'consistency_factor': consistency.get('comeback_factor', 1.0)
            }

            return {
                'key_insights': insights,
                'betting_recommendations': betting_recs,
                'confidence_breakdown': confidence_factors,
                'match_context': {
                    'venue': venue,
                    'round': round_num,
                    'season': season,
                    'weather_conditions': weather_analysis.get('weather_conditions', {}),
                    'upset_factors': upset_analysis.get('upset_factors', [])
                },
                'prediction_quality': self._assess_prediction_quality(predictions, weather_analysis, upset_analysis)
            }

        except Exception as e:
            logger.error(f"Comprehensive analysis generation failed: {e}")
            return {
                'key_insights': ['Standard match prediction'],
                'betting_recommendations': ['Standard betting approach'],
                'confidence_breakdown': {'base_prediction': 60},
                'prediction_quality': 'Standard'
            }

    def _assess_prediction_quality(self, predictions: Dict, weather_analysis: Dict,
                                 upset_analysis: Dict) -> str:
        """Assess overall prediction quality"""
        try:
            quality_score = 0

            # Base confidence
            base_conf = predictions['fulltime'].get('confidence', 60)
            if base_conf > 70:
                quality_score += 2
            elif base_conf > 60:
                quality_score += 1

            # Weather certainty
            if weather_analysis.get('overall_impact') == 'Minimal Impact':
                quality_score += 1

            # Upset likelihood
            upset_likelihood = upset_analysis.get('likelihood_category', 'Low')
            if upset_likelihood == 'Very Low':
                quality_score += 2
            elif upset_likelihood == 'Low':
                quality_score += 1

            # Consistency
            consistency = predictions.get('consistency_check', {})
            if not consistency.get('adjustments_made'):
                quality_score += 1

            # Determine quality rating
            if quality_score >= 5:
                return 'High'
            elif quality_score >= 3:
                return 'Medium'
            else:
                return 'Low'

        except Exception as e:
            logger.error(f"Quality assessment failed: {e}")
            return 'Standard'

    def _apply_weather_adjustments(self, base_prediction: Dict, weather_analysis: Dict,
                                 upset_analysis: Dict) -> Dict:
        """Apply weather and upset adjustments to base prediction"""
        try:
            adjusted_prediction = base_prediction.copy()

            # Apply weather factors
            home_weather = weather_analysis.get('home_weather_factor', 1.0)
            away_weather = weather_analysis.get('away_weather_factor', 1.0)

            # Apply upset confidence adjustment
            upset_adjustment = upset_analysis.get('confidence_adjustment', 1.0)

            # Adjust scores (ensure even numbers)
            adjusted_home_score_float = base_prediction['predicted_home_score'] * home_weather
            adjusted_away_score_float = base_prediction['predicted_away_score'] * away_weather

            adjusted_home_score = (round(adjusted_home_score_float) // 2) * 2
            adjusted_away_score = (round(adjusted_away_score_float) // 2) * 2

            # Adjust confidence
            adjusted_confidence = base_prediction['confidence'] * upset_adjustment

            # Update prediction
            adjusted_prediction.update({
                'predicted_home_score': adjusted_home_score,
                'predicted_away_score': adjusted_away_score,
                'predicted_margin': abs(adjusted_home_score - adjusted_away_score),
                'predicted_total_points': adjusted_home_score + adjusted_away_score,
                'confidence': adjusted_confidence,
                'predicted_winner': base_prediction['predicted_winner'] if adjusted_home_score != adjusted_away_score
                                  else ('Draw' if adjusted_home_score == adjusted_away_score else base_prediction['predicted_winner'])
            })

            return adjusted_prediction

        except Exception as e:
            logger.error(f"Weather adjustment failed: {e}")
            return base_prediction

    def _calculate_halftime_confidence(self, home_patterns: Dict, away_patterns: Dict,
                                     weather_analysis: Dict, upset_analysis: Dict) -> float:
        """Calculate confidence for half-time prediction"""
        try:
            # Base confidence from team consistency
            home_consistency = home_patterns.get('consistency', 0.7)
            away_consistency = away_patterns.get('consistency', 0.7)
            base_confidence = (home_consistency + away_consistency) / 2 * 100

            # Weather impact on confidence
            weather_impact = weather_analysis.get('overall_impact', 'Minimal Impact')
            if weather_impact == 'High Negative Impact':
                weather_factor = 0.9
            elif weather_impact == 'Moderate Negative Impact':
                weather_factor = 0.95
            else:
                weather_factor = 1.0

            # Upset impact on confidence
            upset_factor = upset_analysis.get('confidence_adjustment', 1.0)

            final_confidence = base_confidence * weather_factor * upset_factor
            return max(40, min(85, final_confidence))

        except Exception as e:
            logger.error(f"Half-time confidence calculation failed: {e}")
            return 60.0

    def _calculate_halftime_weather_impact(self, weather_analysis: Dict) -> float:
        """Calculate weather impact specifically for half-time"""
        try:
            # Half-time is less affected by weather (shorter exposure)
            home_factor = weather_analysis.get('home_weather_factor', 1.0)
            away_factor = weather_analysis.get('away_weather_factor', 1.0)

            # Reduce weather impact for half-time (teams adapt during game)
            avg_factor = (home_factor + away_factor) / 2
            ht_factor = 1.0 + (avg_factor - 1.0) * 0.7  # 70% of full game weather impact

            return ht_factor

        except Exception as e:
            logger.error(f"Half-time weather impact calculation failed: {e}")
            return 1.0

    def _calculate_halftime_upset_impact(self, upset_analysis: Dict) -> float:
        """Calculate upset impact specifically for half-time"""
        try:
            # Half-time upsets are less dramatic than full-game upsets
            upset_prob = upset_analysis.get('upset_probability', 0.2)

            # Convert upset probability to scoring adjustment
            # Higher upset probability = more unpredictable scoring
            ht_upset_factor = 1.0 + (upset_prob - 0.2) * 0.3

            return max(0.9, min(1.1, ht_upset_factor))

        except Exception as e:
            logger.error(f"Half-time upset impact calculation failed: {e}")
            return 1.0

    def _calculate_confidence_adjustment(self, upset_probability: float) -> float:
        """Calculate confidence adjustment based on upset probability"""
        try:
            # Higher upset probability = lower confidence
            # Scale from 1.0 (no adjustment) to 0.8 (20% reduction)
            adjustment = 1.0 - (upset_probability * 0.4)
            return max(0.8, min(1.0, adjustment))

        except Exception as e:
            logger.error(f"Confidence adjustment calculation failed: {e}")
            return 1.0

    def _generate_upset_betting_insights(self, upset_probability: float, upset_scenarios: List) -> List[str]:
        """Generate betting insights based on upset analysis"""
        insights = []

        try:
            if upset_probability > 0.3:
                insights.append("High upset potential - consider underdog value bets")
                insights.append("Avoid heavy favorites in this matchup")
            elif upset_probability > 0.2:
                insights.append("Moderate upset risk - smaller stakes recommended")
                insights.append("Consider draw/close margin betting options")
            else:
                insights.append("Low upset risk - favorites likely safe")

            # Scenario-specific insights
            for scenario in upset_scenarios:
                if scenario['probability'] > 0.25:
                    insights.append(f"Watch for {scenario['scenario'].lower()} scenario")

            return insights

        except Exception as e:
            logger.error(f"Upset betting insights generation failed: {e}")
            return ["Standard upset risk assessment"]
