#!/usr/bin/env python3
"""
Test script for Enhanced Comprehensive Prediction System
Tests weather impacts, upset possibilities, and half-time predictions
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.enhanced_match_prediction_service import EnhancedMatchPredictionService
from app.core.database import NRLDatabase

def test_weather_impact_analysis():
    """Test weather impact analysis"""
    print("🌦️  Testing Weather Impact Analysis")
    print("=" * 50)
    
    db = NRLDatabase()
    service = EnhancedMatchPredictionService(db)
    
    # Test different weather conditions
    weather_scenarios = [
        {
            'name': 'Heavy Rain',
            'conditions': {
                'temperature': 15,
                'precipitation': 8,
                'wind_speed': 25,
                'weather_description': 'heavy rain'
            }
        },
        {
            'name': 'Hot and Dry',
            'conditions': {
                'temperature': 35,
                'precipitation': 0,
                'wind_speed': 5,
                'weather_description': 'clear'
            }
        },
        {
            'name': 'Cold and Windy',
            'conditions': {
                'temperature': 8,
                'precipitation': 0,
                'wind_speed': 30,
                'weather_description': 'partly cloudy'
            }
        }
    ]
    
    # Test teams with different weather preferences
    test_teams = [
        ('Melbourne Storm', 'Sydney Roosters'),  # Wet weather specialist vs skill team
        ('Penrith Panthers', 'Parramatta Eels'),  # Power vs skill
        ('Canterbury Bulldogs', 'Manly Sea Eagles')  # Forward vs backs
    ]
    
    for weather in weather_scenarios:
        print(f"\n🌤️  {weather['name']} Conditions:")
        print("-" * 30)
        
        for home_team, away_team in test_teams:
            weather_analysis = service._analyze_weather_impact(
                home_team, away_team, 'ANZ Stadium', weather['conditions']
            )
            
            home_factor = weather_analysis['home_weather_factor']
            away_factor = weather_analysis['away_weather_factor']
            impact = weather_analysis['overall_impact']
            
            print(f"{home_team} vs {away_team}:")
            print(f"  Home Factor: {home_factor:.3f}, Away Factor: {away_factor:.3f}")
            print(f"  Impact: {impact}")
            
            if weather_analysis.get('betting_implications'):
                print(f"  Betting: {weather_analysis['betting_implications'][0]}")

def test_upset_analysis():
    """Test upset possibility analysis"""
    print("\n\n🎯 Testing Upset Analysis")
    print("=" * 50)
    
    db = NRLDatabase()
    service = EnhancedMatchPredictionService(db)
    
    # Test scenarios with different upset potential
    test_scenarios = [
        {
            'home': 'Penrith Panthers', 'away': 'Wests Tigers',
            'round': 10, 'description': 'Strong vs Weak team'
        },
        {
            'home': 'Melbourne Storm', 'away': 'Sydney Roosters',
            'round': 14, 'description': 'Elite teams during Origin'
        },
        {
            'home': 'Brisbane Broncos', 'away': 'Gold Coast Titans',
            'round': 25, 'description': 'Derby match late season'
        },
        {
            'home': 'Cronulla Sharks', 'away': 'Manly Sea Eagles',
            'round': 15, 'description': 'Even teams during Origin'
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n🥊 {scenario['description']}")
        print(f"   {scenario['home']} vs {scenario['away']} (Round {scenario['round']})")
        print("-" * 45)
        
        # Create mock base prediction
        base_prediction = {
            'predicted_winner': scenario['home'],
            'confidence': 65,
            'predicted_margin': 8,
            'predicted_home_score': 24,
            'predicted_away_score': 16
        }
        
        upset_analysis = service._analyze_upset_possibilities(
            scenario['home'], scenario['away'], 2025, scenario['round'], base_prediction
        )
        
        print(f"Upset Probability: {upset_analysis['upset_probability']:.1%}")
        print(f"Likelihood: {upset_analysis['likelihood_category']}")
        print(f"Key Factors: {len(upset_analysis['upset_factors'])} identified")
        
        for factor in upset_analysis['upset_factors']:
            print(f"  • {factor['description']}")

def test_halftime_predictions():
    """Test comprehensive half-time predictions"""
    print("\n\n⏰ Testing Half-Time Predictions")
    print("=" * 50)
    
    db = NRLDatabase()
    service = EnhancedMatchPredictionService(db)
    
    test_matches = [
        ('Penrith Panthers', 'Melbourne Storm', 'High-scoring elite teams'),
        ('Canterbury Bulldogs', 'St George Illawarra Dragons', 'Defensive teams'),
        ('Sydney Roosters', 'Parramatta Eels', 'Fast vs structured'),
        ('Brisbane Broncos', 'Wests Tigers', 'Young vs rebuilding')
    ]
    
    for home_team, away_team, description in test_matches:
        print(f"\n🏉 {description}")
        print(f"   {home_team} vs {away_team}")
        print("-" * 40)
        
        # Mock base prediction
        base_prediction = {
            'predicted_home_score': 26,
            'predicted_away_score': 20,
            'predicted_winner': home_team,
            'confidence': 68
        }
        
        # Mock weather and upset analysis
        weather_analysis = {'home_weather_factor': 1.0, 'away_weather_factor': 1.0}
        upset_analysis = {'upset_probability': 0.2, 'confidence_adjustment': 1.0}
        
        halftime_pred = service._predict_halftime_comprehensive(
            home_team, away_team, 2025, 10, 'ANZ Stadium',
            base_prediction, weather_analysis, upset_analysis
        )
        
        print(f"Half-Time Score: {halftime_pred['ht_home_score']}-{halftime_pred['ht_away_score']}")
        print(f"HT Winner: {halftime_pred['ht_winner']}")
        print(f"HT Confidence: {halftime_pred['ht_confidence']:.1f}%")
        print(f"First Score: {halftime_pred['first_score_analysis']['first_score_team']}")
        print(f"First Score Type: {halftime_pred['first_score_analysis']['first_score_type']}")

def test_comprehensive_prediction():
    """Test full comprehensive prediction"""
    print("\n\n🔮 Testing Comprehensive Prediction")
    print("=" * 50)
    
    db = NRLDatabase()
    service = EnhancedMatchPredictionService(db)
    
    # Test with different conditions
    test_scenarios = [
        {
            'home': 'Melbourne Storm', 'away': 'Sydney Roosters',
            'round': 14, 'venue': 'AAMI Park',
            'weather': {
                'temperature': 12, 'precipitation': 3,
                'wind_speed': 20, 'weather_description': 'light rain'
            },
            'description': 'Elite teams, Origin period, wet weather'
        },
        {
            'home': 'Penrith Panthers', 'away': 'Brisbane Broncos',
            'round': 10, 'venue': 'BlueBet Stadium',
            'weather': None,  # Will use estimated weather
            'description': 'Strong teams, normal conditions'
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n🎯 {scenario['description']}")
        print(f"   {scenario['home']} vs {scenario['away']}")
        print(f"   Round {scenario['round']} at {scenario['venue']}")
        print("-" * 55)
        
        try:
            prediction = service.predict_match_comprehensive(
                scenario['home'], scenario['away'], 2025, scenario['round'],
                scenario['venue'], scenario['weather']
            )
            
            print(f"Winner: {prediction['predicted_winner']}")
            print(f"Score: {prediction['predicted_home_score']}-{prediction['predicted_away_score']}")
            print(f"Confidence: {prediction['confidence']:.1f}%")
            print(f"Total Points: {prediction['predicted_total_points']}")
            
            # Half-time details
            ht = prediction['halftime_prediction']
            print(f"\nHalf-Time: {ht['ht_home_score']}-{ht['ht_away_score']}")
            print(f"HT Winner: {ht['ht_winner']}")
            
            # Weather impact
            weather = prediction['weather_analysis']
            print(f"\nWeather Impact: {weather['overall_impact']}")
            
            # Upset analysis
            upset = prediction['upset_analysis']
            print(f"Upset Risk: {upset['likelihood_category']} ({upset['upset_probability']:.1%})")
            
            # Key insights
            analysis = prediction['comprehensive_analysis']
            print(f"\nKey Insights:")
            for insight in analysis['key_insights'][:3]:
                print(f"  • {insight}")
            
            print(f"\nPrediction Quality: {analysis['prediction_quality']}")
            
        except Exception as e:
            print(f"Prediction failed: {e}")

def test_consistency_checks():
    """Test prediction consistency between half-time and full-time"""
    print("\n\n✅ Testing Prediction Consistency")
    print("=" * 50)
    
    db = NRLDatabase()
    service = EnhancedMatchPredictionService(db)
    
    # Test scenarios that might need consistency adjustments
    test_cases = [
        {
            'name': 'Normal consistency',
            'fulltime': {'predicted_home_score': 24, 'predicted_away_score': 18, 'predicted_winner': 'Home Team'},
            'halftime': {'ht_home_score': 12, 'ht_away_score': 8, 'ht_winner': 'Home Team'}
        },
        {
            'name': 'HT score too high',
            'fulltime': {'predicted_home_score': 20, 'predicted_away_score': 16, 'predicted_winner': 'Home Team'},
            'halftime': {'ht_home_score': 22, 'ht_away_score': 14, 'ht_winner': 'Home Team'}
        },
        {
            'name': 'Comeback scenario',
            'fulltime': {'predicted_home_score': 26, 'predicted_away_score': 24, 'predicted_winner': 'Home Team'},
            'halftime': {'ht_home_score': 8, 'ht_away_score': 18, 'ht_winner': 'Away Team'}
        }
    ]
    
    for case in test_cases:
        print(f"\n📊 {case['name']}")
        print("-" * 25)
        
        consistent = service._ensure_prediction_consistency(
            case['fulltime'], case['halftime']
        )
        
        ft = consistent['fulltime']
        ht = consistent['halftime']
        check = consistent['consistency_check']
        
        print(f"Full-Time: {ft['predicted_home_score']}-{ft['predicted_away_score']}")
        print(f"Half-Time: {ht['ht_home_score']}-{ht['ht_away_score']}")
        print(f"Second Half: {check['second_half_home']}-{check['second_half_away']}")
        
        if check['adjustments_made']:
            print("Adjustments made:")
            for adj in check['adjustments_made']:
                print(f"  • {adj}")
        else:
            print("No adjustments needed")

def main():
    """Run all comprehensive prediction tests"""
    print("🏉 Enhanced Comprehensive Prediction System Test")
    print("=" * 60)
    
    try:
        test_weather_impact_analysis()
        test_upset_analysis()
        test_halftime_predictions()
        test_comprehensive_prediction()
        test_consistency_checks()
        
        print("\n\n✅ All tests completed successfully!")
        print("\nEnhanced Features Demonstrated:")
        print("• Comprehensive weather impact analysis")
        print("• Sophisticated upset possibility detection")
        print("• Detailed half-time predictions with TMP factors")
        print("• Consistency checks between HT and FT predictions")
        print("• First score predictions")
        print("• Weather-based betting insights")
        print("• Upset scenario analysis")
        print("• Prediction quality assessment")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
