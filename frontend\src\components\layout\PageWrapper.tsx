import React from 'react';
import { Box, BoxProps } from '@mui/material';

interface PageWrapperProps extends BoxProps {
  children: React.ReactNode;
  noPadding?: boolean;
  fullHeight?: boolean;
}

const PageWrapper: React.FC<PageWrapperProps> = ({ 
  children, 
  noPadding = false, 
  fullHeight = false,
  sx,
  ...props 
}) => {
  return (
    <Box
      sx={{
        // Dynamic padding that anchors to sidebar edge
        p: noPadding ? 0 : 3,
        pt: noPadding ? 0 : 2, // Reduced top padding since header spacing is handled in App.tsx
        width: '100%',
        minHeight: fullHeight ? 'calc(100vh - 64px)' : 'auto',
        // Ensure content starts right at the sidebar edge
        margin: 0,
        // Prevent any overflow
        overflow: 'hidden',
        // Smooth transitions when sidebar toggles
        transition: 'padding 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        // Ensure proper box sizing
        boxSizing: 'border-box',
        ...sx,
      }}
      {...props}
    >
      {children}
    </Box>
  );
};

export default PageWrapper;
