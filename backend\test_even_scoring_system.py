#!/usr/bin/env python3
"""
Test script to verify all scores are even numbers throughout the system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.advanced_prediction_service import AdvancedMLPredictionService
from app.services.enhanced_match_prediction_service import EnhancedMatchPredictionService
from app.services.prediction_service import EnhancedPredictionService
from app.core.database import NRLDatabase

def test_advanced_prediction_even_scores():
    """Test advanced prediction service for even scores"""
    print("🔢 Testing Advanced Prediction Service - Even Scores")
    print("=" * 60)
    
    db = NRLDatabase()
    service = AdvancedMLPredictionService(db)
    
    test_matches = [
        ('Penrith Panthers', 'Melbourne Storm'),
        ('Sydney Roosters', 'Brisbane Broncos'),
        ('Canterbury Bulldogs', 'Wests Tigers'),
        ('Manly Sea Eagles', 'Cronulla Sharks'),
        ('Parramatta Eels', 'South Sydney Rabbitohs')
    ]
    
    all_even = True
    
    for home_team, away_team in test_matches:
        try:
            prediction = service.predict_match_advanced(
                home_team, away_team, 2025, 10, "ANZ Stadium"
            )
            
            home_score = prediction['predicted_home_score']
            away_score = prediction['predicted_away_score']
            
            home_even = home_score % 2 == 0
            away_even = away_score % 2 == 0
            
            status = "✅" if (home_even and away_even) else "❌"
            print(f"{status} {home_team} vs {away_team}: {home_score}-{away_score}")
            
            if not (home_even and away_even):
                all_even = False
                print(f"   ⚠️  Odd scores detected!")
                
        except Exception as e:
            print(f"❌ {home_team} vs {away_team}: Prediction failed - {e}")
    
    print(f"\n📊 Advanced Prediction Even Scores: {'✅ PASS' if all_even else '❌ FAIL'}")
    return all_even

def test_enhanced_prediction_even_scores():
    """Test enhanced prediction service for even scores"""
    print("\n\n🌟 Testing Enhanced Prediction Service - Even Scores")
    print("=" * 60)
    
    db = NRLDatabase()
    service = EnhancedMatchPredictionService(db)
    
    test_matches = [
        ('Melbourne Storm', 'Sydney Roosters', {'temperature': 15, 'precipitation': 2}),
        ('Penrith Panthers', 'Brisbane Broncos', None),
        ('Canterbury Bulldogs', 'Manly Sea Eagles', {'temperature': 8, 'wind_speed': 25})
    ]
    
    all_even = True
    
    for home_team, away_team, weather in test_matches:
        try:
            prediction = service.predict_match_comprehensive(
                home_team, away_team, 2025, 14, "ANZ Stadium", weather
            )
            
            # Check full-time scores
            ft_home = prediction['predicted_home_score']
            ft_away = prediction['predicted_away_score']
            
            # Check half-time scores
            ht_home = prediction['halftime_prediction']['ht_home_score']
            ht_away = prediction['halftime_prediction']['ht_away_score']
            
            ft_home_even = ft_home % 2 == 0
            ft_away_even = ft_away % 2 == 0
            ht_home_even = ht_home % 2 == 0
            ht_away_even = ht_away % 2 == 0
            
            all_scores_even = ft_home_even and ft_away_even and ht_home_even and ht_away_even
            
            status = "✅" if all_scores_even else "❌"
            print(f"{status} {home_team} vs {away_team}:")
            print(f"   Full-Time: {ft_home}-{ft_away} {'✅' if (ft_home_even and ft_away_even) else '❌'}")
            print(f"   Half-Time: {ht_home}-{ht_away} {'✅' if (ht_home_even and ht_away_even) else '❌'}")
            
            if not all_scores_even:
                all_even = False
                print(f"   ⚠️  Odd scores detected!")
                
        except Exception as e:
            print(f"❌ {home_team} vs {away_team}: Prediction failed - {e}")
    
    print(f"\n📊 Enhanced Prediction Even Scores: {'✅ PASS' if all_even else '❌ FAIL'}")
    return all_even

def test_basic_prediction_even_scores():
    """Test basic prediction service for even scores"""
    print("\n\n📈 Testing Basic Prediction Service - Even Scores")
    print("=" * 60)
    
    db = NRLDatabase()
    service = EnhancedPredictionService(db)
    
    test_matches = [
        ('Penrith Panthers', 'Melbourne Storm'),
        ('Sydney Roosters', 'Brisbane Broncos'),
        ('Canterbury Bulldogs', 'Wests Tigers')
    ]
    
    all_even = True
    
    for home_team, away_team in test_matches:
        try:
            prediction = service.predict_match_enhanced(home_team, away_team, 2025, 10)
            
            home_score = prediction['predicted_home_score']
            away_score = prediction['predicted_away_score']
            
            home_even = home_score % 2 == 0
            away_even = away_score % 2 == 0
            
            status = "✅" if (home_even and away_even) else "❌"
            print(f"{status} {home_team} vs {away_team}: {home_score}-{away_score}")
            
            if not (home_even and away_even):
                all_even = False
                print(f"   ⚠️  Odd scores detected!")
                
        except Exception as e:
            print(f"❌ {home_team} vs {away_team}: Prediction failed - {e}")
    
    print(f"\n📊 Basic Prediction Even Scores: {'✅ PASS' if all_even else '❌ FAIL'}")
    return all_even

def test_rugby_score_rounding():
    """Test the rugby score rounding function directly"""
    print("\n\n🏉 Testing Rugby Score Rounding Function")
    print("=" * 60)
    
    db = NRLDatabase()
    service = AdvancedMLPredictionService(db)
    
    test_scores = [1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29]
    all_even = True
    
    print("Input Score → Rounded Score")
    print("-" * 30)
    
    for score in test_scores:
        rounded = service._round_to_rugby_score(float(score))
        is_even = rounded % 2 == 0
        
        status = "✅" if is_even else "❌"
        print(f"{status} {score:2d} → {rounded:2d}")
        
        if not is_even:
            all_even = False
    
    print(f"\n📊 Rugby Score Rounding: {'✅ PASS' if all_even else '❌ FAIL'}")
    return all_even

def test_origin_impact_even_scores():
    """Test Origin impact maintains even scores"""
    print("\n\n🏆 Testing Origin Impact - Even Scores")
    print("=" * 60)
    
    db = NRLDatabase()
    service = AdvancedMLPredictionService(db)
    
    # Test Origin rounds
    origin_rounds = [14, 15, 17]
    test_teams = [
        ('Penrith Panthers', 'Brisbane Broncos'),  # High Origin representation
        ('Melbourne Storm', 'New Zealand Warriors'),  # Mixed representation
    ]
    
    all_even = True
    
    for round_num in origin_rounds:
        print(f"\n🎯 Round {round_num} (Origin Round):")
        
        for home_team, away_team in test_teams:
            try:
                prediction = service.predict_match_advanced(
                    home_team, away_team, 2025, round_num, "ANZ Stadium"
                )
                
                home_score = prediction['predicted_home_score']
                away_score = prediction['predicted_away_score']
                
                home_even = home_score % 2 == 0
                away_even = away_score % 2 == 0
                
                status = "✅" if (home_even and away_even) else "❌"
                print(f"  {status} {home_team} vs {away_team}: {home_score}-{away_score}")
                
                if not (home_even and away_even):
                    all_even = False
                    print(f"     ⚠️  Odd scores during Origin period!")
                    
            except Exception as e:
                print(f"  ❌ {home_team} vs {away_team}: Prediction failed - {e}")
    
    print(f"\n📊 Origin Impact Even Scores: {'✅ PASS' if all_even else '❌ FAIL'}")
    return all_even

def test_consistency_check_even_scores():
    """Test that consistency checks maintain even scores"""
    print("\n\n🔄 Testing Consistency Checks - Even Scores")
    print("=" * 60)
    
    db = NRLDatabase()
    service = EnhancedMatchPredictionService(db)
    
    # Test cases that might trigger consistency adjustments
    test_cases = [
        {
            'name': 'HT score too high',
            'fulltime': {'predicted_home_score': 20, 'predicted_away_score': 16, 'predicted_winner': 'Home Team'},
            'halftime': {'ht_home_score': 22, 'ht_away_score': 14, 'ht_winner': 'Home Team'}
        },
        {
            'name': 'Large comeback scenario',
            'fulltime': {'predicted_home_score': 26, 'predicted_away_score': 24, 'predicted_winner': 'Home Team'},
            'halftime': {'ht_home_score': 8, 'ht_away_score': 18, 'ht_winner': 'Away Team'}
        }
    ]
    
    all_even = True
    
    for case in test_cases:
        print(f"\n🧪 {case['name']}:")
        
        try:
            consistent = service._ensure_prediction_consistency(
                case['fulltime'], case['halftime']
            )
            
            ft_home = consistent['fulltime']['predicted_home_score']
            ft_away = consistent['fulltime']['predicted_away_score']
            ht_home = consistent['halftime']['ht_home_score']
            ht_away = consistent['halftime']['ht_away_score']
            
            ft_home_even = ft_home % 2 == 0
            ft_away_even = ft_away % 2 == 0
            ht_home_even = ht_home % 2 == 0
            ht_away_even = ht_away % 2 == 0
            
            all_scores_even = ft_home_even and ft_away_even and ht_home_even and ht_away_even
            
            status = "✅" if all_scores_even else "❌"
            print(f"  {status} Full-Time: {ft_home}-{ft_away}")
            print(f"  {status} Half-Time: {ht_home}-{ht_away}")
            
            if not all_scores_even:
                all_even = False
                print(f"     ⚠️  Odd scores after consistency check!")
                
        except Exception as e:
            print(f"  ❌ Consistency check failed: {e}")
    
    print(f"\n📊 Consistency Check Even Scores: {'✅ PASS' if all_even else '❌ FAIL'}")
    return all_even

def main():
    """Run all even scoring tests"""
    print("🏉 NRL Predictor - Even Scoring System Test")
    print("=" * 70)
    
    test_results = []
    
    try:
        # Test all prediction services
        test_results.append(test_rugby_score_rounding())
        test_results.append(test_advanced_prediction_even_scores())
        test_results.append(test_enhanced_prediction_even_scores())
        test_results.append(test_basic_prediction_even_scores())
        test_results.append(test_origin_impact_even_scores())
        test_results.append(test_consistency_check_even_scores())
        
        # Overall results
        all_passed = all(test_results)
        passed_count = sum(test_results)
        total_count = len(test_results)
        
        print("\n\n" + "=" * 70)
        print("📊 FINAL RESULTS")
        print("=" * 70)
        print(f"Tests Passed: {passed_count}/{total_count}")
        print(f"Overall Status: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
        
        if all_passed:
            print("\n🎉 All scores are now even numbers throughout the system!")
            print("✅ Rugby scoring system properly implemented")
            print("✅ Half-time predictions use even numbers")
            print("✅ Origin impact maintains even scores")
            print("✅ Consistency checks preserve even numbers")
        else:
            print("\n⚠️  Some tests failed - odd scores still present in system")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
