"""
Enhanced prediction service using comprehensive data analysis
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class EnhancedPredictionService:
    """Enhanced prediction service using all available data"""
    
    def __init__(self, db):
        self.db = db
        self.model_version = "enhanced_v2.0"
        
    def get_comprehensive_team_stats(self, team: str, season: int, current_round: int) -> Dict:
        """Get comprehensive team statistics including recent form"""
        try:
            # Get basic team stats
            basic_stats = self.db.get_team_stats(team, season)
            if not basic_stats:
                return {}
            
            # Get recent matches for form analysis
            matches_df = self.db.get_matches_df(season=season, team=team)
            if matches_df.empty:
                return basic_stats
            
            # Filter completed matches before current round
            completed_matches = matches_df[
                (matches_df['round'] < current_round) & 
                (matches_df['home_score'].notna()) & 
                (matches_df['away_score'].notna())
            ].sort_values('round')
            
            if completed_matches.empty:
                return basic_stats
            
            # Calculate recent form (last 5 matches)
            recent_matches = completed_matches.tail(5)
            recent_wins = 0
            recent_points_for = 0
            recent_points_against = 0
            
            for _, match in recent_matches.iterrows():
                if match['home_team'] == team:
                    recent_points_for += match['home_score']
                    recent_points_against += match['away_score']
                    if match['home_score'] > match['away_score']:
                        recent_wins += 1
                else:
                    recent_points_for += match['away_score']
                    recent_points_against += match['home_score']
                    if match['away_score'] > match['home_score']:
                        recent_wins += 1
            
            # Calculate form metrics
            recent_form = recent_wins / len(recent_matches) if len(recent_matches) > 0 else 0
            recent_avg_for = recent_points_for / len(recent_matches) if len(recent_matches) > 0 else 0
            recent_avg_against = recent_points_against / len(recent_matches) if len(recent_matches) > 0 else 0
            
            # Home/Away performance
            home_matches = completed_matches[completed_matches['home_team'] == team]
            away_matches = completed_matches[completed_matches['away_team'] == team]
            
            home_wins = len(home_matches[home_matches['home_score'] > home_matches['away_score']])
            away_wins = len(away_matches[away_matches['away_score'] > away_matches['home_score']])
            
            home_win_rate = home_wins / len(home_matches) if len(home_matches) > 0 else 0
            away_win_rate = away_wins / len(away_matches) if len(away_matches) > 0 else 0
            
            # Enhanced stats
            enhanced_stats = basic_stats.copy()
            enhanced_stats.update({
                'recent_form': recent_form,
                'recent_avg_for': recent_avg_for,
                'recent_avg_against': recent_avg_against,
                'home_win_rate': home_win_rate,
                'away_win_rate': away_win_rate,
                'form_trend': self._calculate_form_trend(recent_matches, team),
                'consistency': self._calculate_consistency(completed_matches, team)
            })
            
            return enhanced_stats
            
        except Exception as e:
            logger.error(f"Error getting comprehensive stats for {team}: {e}")
            return self.db.get_team_stats(team, season) or {}
    
    def _calculate_form_trend(self, recent_matches: pd.DataFrame, team: str) -> float:
        """Calculate form trend (improving/declining)"""
        if len(recent_matches) < 3:
            return 0.0
        
        # Get points difference for each match
        point_diffs = []
        for _, match in recent_matches.iterrows():
            if match['home_team'] == team:
                diff = match['home_score'] - match['away_score']
            else:
                diff = match['away_score'] - match['home_score']
            point_diffs.append(diff)
        
        # Calculate trend (positive = improving, negative = declining)
        if len(point_diffs) >= 3:
            recent_avg = np.mean(point_diffs[-3:])
            earlier_avg = np.mean(point_diffs[:-3]) if len(point_diffs) > 3 else np.mean(point_diffs[:2])
            return recent_avg - earlier_avg
        
        return 0.0
    
    def _calculate_consistency(self, matches: pd.DataFrame, team: str) -> float:
        """Calculate team consistency (lower variance = more consistent)"""
        if len(matches) < 3:
            return 0.5
        
        point_diffs = []
        for _, match in matches.iterrows():
            if match['home_team'] == team:
                diff = match['home_score'] - match['away_score']
            else:
                diff = match['away_score'] - match['home_score']
            point_diffs.append(diff)
        
        # Return inverse of coefficient of variation (normalized)
        if len(point_diffs) > 0 and np.std(point_diffs) > 0:
            cv = np.std(point_diffs) / (abs(np.mean(point_diffs)) + 1)
            return max(0, min(1, 1 - (cv / 2)))  # Normalize to 0-1
        
        return 0.5
    
    def get_venue_factor(self, venue: str, home_team: str) -> float:
        """Calculate venue advantage factor"""
        try:
            # Get historical matches at this venue
            venue_matches = self.db.get_matches_df(venue=venue)
            if venue_matches.empty:
                return 1.05  # Default home advantage
            
            # Calculate home team advantage at this venue
            home_matches = venue_matches[venue_matches['home_team'] == home_team]
            if len(home_matches) < 3:
                return 1.05  # Default if insufficient data
            
            completed_home = home_matches[home_matches['home_score'].notna()]
            if len(completed_home) == 0:
                return 1.05
            
            home_wins = len(completed_home[completed_home['home_score'] > completed_home['away_score']])
            home_win_rate = home_wins / len(completed_home)
            
            # Convert win rate to advantage factor (0.5 = no advantage, >0.5 = advantage)
            advantage_factor = 1.0 + (home_win_rate - 0.5) * 0.2  # Max 10% advantage
            return max(0.95, min(1.15, advantage_factor))
            
        except Exception as e:
            logger.error(f"Error calculating venue factor: {e}")
            return 1.05
    
    def predict_match_enhanced(self, home_team: str, away_team: str, season: int, 
                             round_num: int, venue: str = None) -> Dict:
        """Enhanced match prediction using comprehensive data"""
        try:
            # Get comprehensive stats for both teams
            home_stats = self.get_comprehensive_team_stats(home_team, season, round_num)
            away_stats = self.get_comprehensive_team_stats(away_team, season, round_num)
            
            if not home_stats or not away_stats:
                raise ValueError("Unable to find comprehensive team statistics")
            
            # Base prediction factors
            home_win_rate = home_stats.get('win_percentage', 50) / 100
            away_win_rate = away_stats.get('win_percentage', 50) / 100
            
            # Recent form factors
            home_form = home_stats.get('recent_form', 0.5)
            away_form = away_stats.get('recent_form', 0.5)
            
            # Form trend factors
            home_trend = home_stats.get('form_trend', 0)
            away_trend = away_stats.get('form_trend', 0)
            
            # Venue factor
            venue_factor = self.get_venue_factor(venue, home_team) if venue else 1.05
            
            # Calculate weighted win probability
            home_probability = (
                home_win_rate * 0.4 +  # Historical performance
                home_form * 0.3 +      # Recent form
                (home_trend / 20 + 0.5) * 0.1 +  # Form trend
                (venue_factor - 1) * 2 +  # Venue advantage
                0.05  # Base home advantage
            )
            
            away_probability = (
                away_win_rate * 0.4 +
                away_form * 0.3 +
                (away_trend / 20 + 0.5) * 0.1 +
                0.05  # Small away team motivation
            )
            
            # Normalize probabilities
            total_prob = home_probability + away_probability
            if total_prob > 0:
                home_probability = home_probability / total_prob
                away_probability = away_probability / total_prob
            
            # Determine winner and confidence
            if home_probability > away_probability:
                predicted_winner = home_team
                confidence = home_probability * 100
            else:
                predicted_winner = away_team
                confidence = away_probability * 100
            
            # Predict scores based on recent averages and form
            home_avg_for = home_stats.get('recent_avg_for', home_stats.get('avg_points_for', 20))
            away_avg_for = away_stats.get('recent_avg_for', away_stats.get('avg_points_for', 20))

            # Adjust for venue and form
            predicted_home_score_float = home_avg_for * venue_factor * (1 + home_form * 0.2)
            predicted_away_score_float = away_avg_for * (1 + away_form * 0.2)

            # Round to even numbers
            predicted_home_score = (round(predicted_home_score_float) // 2) * 2
            predicted_away_score = (round(predicted_away_score_float) // 2) * 2

            # Ensure realistic scores (even numbers)
            predicted_home_score = max(6, min(50, predicted_home_score))
            predicted_away_score = max(6, min(50, predicted_away_score))

            # Final even number check
            predicted_home_score = (predicted_home_score // 2) * 2
            predicted_away_score = (predicted_away_score // 2) * 2
            
            return {
                'predicted_winner': predicted_winner,
                'confidence': round(confidence, 1),
                'predicted_home_score': predicted_home_score,
                'predicted_away_score': predicted_away_score,
                'predicted_margin': abs(predicted_home_score - predicted_away_score),
                'predicted_total_points': predicted_home_score + predicted_away_score,
                'factors': {
                    'home_win_rate': round(home_win_rate * 100, 1),
                    'away_win_rate': round(away_win_rate * 100, 1),
                    'home_recent_form': round(home_form * 100, 1),
                    'away_recent_form': round(away_form * 100, 1),
                    'venue_factor': round(venue_factor, 3),
                    'home_trend': round(home_trend, 1),
                    'away_trend': round(away_trend, 1),
                    'data_quality': 'comprehensive'
                }
            }
            
        except Exception as e:
            logger.error(f"Enhanced prediction failed: {e}")
            # Fallback to basic prediction
            return self._basic_prediction_fallback(home_team, away_team, season)
    
    def _basic_prediction_fallback(self, home_team: str, away_team: str, season: int) -> Dict:
        """Fallback to basic prediction if enhanced fails"""
        try:
            home_stats = self.db.get_team_stats(home_team, season)
            away_stats = self.db.get_team_stats(away_team, season)
            
            if not home_stats or not away_stats:
                # Ultimate fallback
                return {
                    'predicted_winner': home_team,
                    'confidence': 55.0,
                    'predicted_home_score': 20,
                    'predicted_away_score': 18,
                    'predicted_margin': 2,
                    'predicted_total_points': 38,
                    'factors': {'data_quality': 'minimal'}
                }
            
            home_win_rate = home_stats.get('win_percentage', 50) / 100
            away_win_rate = away_stats.get('win_percentage', 50) / 100
            
            # Simple calculation with home advantage
            home_probability = home_win_rate * 0.8 + 0.15  # 15% home advantage
            away_probability = away_win_rate * 0.8 + 0.05
            
            if home_probability > away_probability:
                predicted_winner = home_team
                confidence = home_probability * 100
            else:
                predicted_winner = away_team
                confidence = away_probability * 100
            
            # Calculate scores and ensure even numbers
            predicted_home_score_float = home_stats.get('avg_points_for', 20) * 1.05
            predicted_away_score_float = away_stats.get('avg_points_for', 20)

            predicted_home_score = (round(predicted_home_score_float) // 2) * 2
            predicted_away_score = (round(predicted_away_score_float) // 2) * 2
            
            return {
                'predicted_winner': predicted_winner,
                'confidence': round(confidence, 1),
                'predicted_home_score': predicted_home_score,
                'predicted_away_score': predicted_away_score,
                'predicted_margin': abs(predicted_home_score - predicted_away_score),
                'predicted_total_points': predicted_home_score + predicted_away_score,
                'factors': {
                    'home_win_rate': round(home_win_rate * 100, 1),
                    'away_win_rate': round(away_win_rate * 100, 1),
                    'data_quality': 'basic'
                }
            }
            
        except Exception as e:
            logger.error(f"Basic prediction fallback failed: {e}")
            return {
                'predicted_winner': home_team,
                'confidence': 55.0,
                'predicted_home_score': 20,
                'predicted_away_score': 18,
                'predicted_margin': 2,
                'predicted_total_points': 38,
                'factors': {'data_quality': 'emergency_fallback'}
            }
