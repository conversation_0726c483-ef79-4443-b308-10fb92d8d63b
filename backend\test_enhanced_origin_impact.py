#!/usr/bin/env python3
"""
Test script for Enhanced State of Origin and Key Player Impact System
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.player_impact_service import PlayerImpactService
from app.services.advanced_prediction_service import AdvancedMLPredictionService
from app.core.database import NRLDatabase

def test_origin_impact_analysis():
    """Test State of Origin impact analysis"""
    print("🏉 Testing Enhanced State of Origin Impact Analysis")
    print("=" * 60)
    
    # Initialize services
    db = NRLDatabase()
    player_service = PlayerImpactService(db)
    
    # Test teams with different Origin representation
    test_teams = [
        'Penrith Panthers',      # High NSW representation
        'Brisbane Broncos',      # High QLD representation  
        'New Zealand Warriors',  # No Origin players
        'Melbourne Storm',       # Mixed representation
        'Canberra Raiders'       # Low representation
    ]
    
    # Test different rounds
    test_rounds = [10, 14, 15, 17, 20]  # Normal, Origin rounds, post-Origin
    season = 2025
    
    for team in test_teams:
        print(f"\n🏆 Team: {team}")
        print("-" * 40)
        
        for round_num in test_rounds:
            origin_analysis = player_service.analyze_state_of_origin_impact(team, season, round_num)
            
            print(f"Round {round_num:2d}: ", end="")
            if origin_analysis['is_origin_affected']:
                intensity = origin_analysis['origin_intensity']
                factor = origin_analysis['scoring_impact_factor']
                players = origin_analysis['team_origin_players']
                print(f"AFFECTED ({intensity}) - {factor:.2f}x scoring, {players} Origin players")
            else:
                print("Normal round - No Origin impact")

def test_key_player_strengths():
    """Test key player strength analysis"""
    print("\n\n🌟 Testing Key Player Strength Analysis")
    print("=" * 60)
    
    db = NRLDatabase()
    player_service = PlayerImpactService(db)
    
    # Test elite vs developing teams
    test_teams = [
        'Penrith Panthers',    # Elite team
        'Melbourne Storm',     # Structured team
        'Brisbane Broncos',    # Young talent
        'Wests Tigers',        # Rebuilding team
        'Gold Coast Titans'    # Limited threats
    ]
    
    season = 2025
    
    for team in test_teams:
        print(f"\n🏆 {team}")
        print("-" * 30)
        
        analysis = player_service.analyze_key_player_strengths(team, season)
        
        strength_rating = analysis['overall_strength_rating']
        primary_threat = analysis['scoring_patterns']['primary_threat']
        impact_summary = analysis['impact_summary']
        
        print(f"Strength Rating: {strength_rating:.2f}")
        print(f"Primary Threat:  {primary_threat}")
        print(f"Summary:         {impact_summary}")
        
        # Show positional strengths
        positional = analysis['positional_strengths']
        print(f"Positional Impact: {positional['total_positional_impact']:.2f}")

def test_combined_match_impact():
    """Test combined impact for specific matches"""
    print("\n\n⚔️  Testing Combined Match Impact")
    print("=" * 60)
    
    db = NRLDatabase()
    player_service = PlayerImpactService(db)
    
    # Test interesting matchups
    test_matches = [
        ('Penrith Panthers', 'Melbourne Storm', 14),    # Elite vs Elite during Origin
        ('Brisbane Broncos', 'New Zealand Warriors', 15), # QLD heavy vs No Origin
        ('Wests Tigers', 'Gold Coast Titans', 10),      # Rebuilding teams normal round
        ('Sydney Roosters', 'South Sydney Rabbitohs', 17) # Derby during Origin
    ]
    
    season = 2025
    
    for home_team, away_team, round_num in test_matches:
        print(f"\n🥊 {home_team} vs {away_team} (Round {round_num})")
        print("-" * 50)
        
        combined_impact = player_service.calculate_combined_impact(
            home_team, away_team, season, round_num
        )
        
        home_factor = combined_impact['home_team_impact']['combined_scoring_factor']
        away_factor = combined_impact['away_team_impact']['combined_scoring_factor']
        context = combined_impact['match_analysis']['match_context']
        
        print(f"Match Context:     {context}")
        print(f"Home Scoring:      {home_factor:.3f}x")
        print(f"Away Scoring:      {away_factor:.3f}x")
        print(f"Expected Impact:   {combined_impact['match_analysis']['expected_total_impact']:.3f}")
        
        # Show recommendations
        recommendations = combined_impact['recommendations']
        if recommendations:
            print("Betting Recommendations:")
            for rec in recommendations[:2]:  # Show first 2
                print(f"  • {rec}")

def test_prediction_integration():
    """Test integration with advanced prediction service"""
    print("\n\n🔮 Testing Prediction Service Integration")
    print("=" * 60)
    
    db = NRLDatabase()
    prediction_service = AdvancedMLPredictionService(db)
    
    # Test prediction with enhanced Origin impact
    test_matches = [
        ('Penrith Panthers', 'Brisbane Broncos', 14),   # Origin Game 1
        ('Melbourne Storm', 'Sydney Roosters', 15),     # Origin Game 2  
        ('New Zealand Warriors', 'Canberra Raiders', 17) # Origin Game 3 (minimal impact)
    ]
    
    season = 2025
    
    for home_team, away_team, round_num in test_matches:
        print(f"\n🎯 Predicting: {home_team} vs {away_team} (Round {round_num})")
        print("-" * 55)
        
        try:
            prediction = prediction_service.predict_match_advanced(
                home_team, away_team, season, round_num, "ANZ Stadium"
            )
            
            print(f"Winner:           {prediction['predicted_winner']}")
            print(f"Confidence:       {prediction['confidence']}%")
            print(f"Score:            {prediction['predicted_home_score']}-{prediction['predicted_away_score']}")
            print(f"Total Points:     {prediction['predicted_total_points']}")
            
            factors = prediction.get('factors', {})
            if 'state_of_origin_impact' in factors:
                print(f"Origin Impact:    {factors['state_of_origin_impact']}")
            if 'key_player_strengths' in factors:
                print(f"Player Analysis:  {factors['key_player_strengths']}")
                
        except Exception as e:
            print(f"Prediction failed: {e}")
            print("(This is expected if ML models aren't trained yet)")

def main():
    """Run all tests"""
    print("🏉 Enhanced State of Origin & Key Player Impact System Test")
    print("=" * 70)
    
    try:
        test_origin_impact_analysis()
        test_key_player_strengths()
        test_combined_match_impact()
        test_prediction_integration()
        
        print("\n\n✅ All tests completed successfully!")
        print("\nKey Features Demonstrated:")
        print("• Dynamic State of Origin round detection")
        print("• Team-specific Origin player impact analysis")
        print("• Comprehensive key player strength assessment")
        print("• Positional impact analysis")
        print("• Scoring pattern analysis")
        print("• Combined match impact calculation")
        print("• Integration with prediction service")
        print("• Betting recommendation generation")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
