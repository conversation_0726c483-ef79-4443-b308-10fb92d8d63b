"""
Enhanced Player Impact Service
Analyzes key player strengths and their impact on team scoring patterns
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class PlayerImpactService:
    """Service for analyzing player impact on team performance"""
    
    def __init__(self, db=None):
        if db is None:
            from app.core.database import NRLDatabase
            db = NRLDatabase()
        self.db = db
        
    def analyze_state_of_origin_impact(self, team: str, season: int, round_num: int) -> Dict:
        """Comprehensive State of Origin impact analysis"""
        try:
            # Get Origin schedule for season
            origin_schedule = self._get_origin_schedule(season)
            
            # Determine if current round is affected by Origin
            origin_impact = self._calculate_origin_round_impact(round_num, origin_schedule)
            
            # Get team's Origin player count and impact
            team_origin_impact = self._get_team_origin_impact(team, origin_impact)
            
            # Historical performance during Origin periods
            historical_impact = self._get_historical_origin_performance(team, season)
            
            return {
                'is_origin_affected': origin_impact['is_affected'],
                'origin_intensity': origin_impact['intensity'],
                'team_origin_players': team_origin_impact['player_count'],
                'scoring_impact_factor': team_origin_impact['scoring_factor'],
                'historical_performance': historical_impact,
                'detailed_analysis': {
                    'origin_schedule': origin_schedule,
                    'round_analysis': origin_impact,
                    'team_analysis': team_origin_impact
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing Origin impact for {team}: {e}")
            return self._get_default_origin_impact()
    
    def analyze_key_player_strengths(self, team: str, season: int) -> Dict:
        """Analyze key player strengths and their scoring impact"""
        try:
            # Get team's key player profile
            player_profile = self._get_enhanced_player_profile(team)
            
            # Calculate positional strength impact
            positional_impact = self._calculate_positional_impact(player_profile)
            
            # Analyze scoring pattern influence
            scoring_patterns = self._analyze_scoring_patterns(team, player_profile)
            
            # Calculate overall team strength rating
            strength_rating = self._calculate_team_strength_rating(player_profile, positional_impact)
            
            return {
                'overall_strength_rating': strength_rating,
                'positional_strengths': positional_impact,
                'scoring_patterns': scoring_patterns,
                'key_player_profile': player_profile,
                'impact_summary': self._generate_impact_summary(player_profile, strength_rating)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing key player strengths for {team}: {e}")
            return self._get_default_player_analysis()
    
    def calculate_combined_impact(self, home_team: str, away_team: str, season: int, round_num: int) -> Dict:
        """Calculate combined player and Origin impact for match prediction"""
        try:
            # Get individual team analyses
            home_origin = self.analyze_state_of_origin_impact(home_team, season, round_num)
            away_origin = self.analyze_state_of_origin_impact(away_team, season, round_num)
            
            home_players = self.analyze_key_player_strengths(home_team, season)
            away_players = self.analyze_key_player_strengths(away_team, season)
            
            # Calculate match-specific impacts
            match_impact = self._calculate_match_impact(
                home_origin, away_origin, home_players, away_players, round_num
            )
            
            return {
                'home_team_impact': {
                    'origin_impact': home_origin,
                    'player_strengths': home_players,
                    'combined_scoring_factor': match_impact['home_scoring_factor']
                },
                'away_team_impact': {
                    'origin_impact': away_origin,
                    'player_strengths': away_players,
                    'combined_scoring_factor': match_impact['away_scoring_factor']
                },
                'match_analysis': match_impact,
                'recommendations': self._generate_betting_recommendations(match_impact)
            }
            
        except Exception as e:
            logger.error(f"Error calculating combined impact: {e}")
            return self._get_default_combined_impact()
    
    def _get_origin_schedule(self, season: int) -> Dict:
        """Get State of Origin schedule for season"""
        # Enhanced Origin schedule with more detail
        schedules = {
            2024: {
                'games': [
                    {'game': 1, 'round': 14, 'date': '2024-06-05', 'venue': 'Accor Stadium'},
                    {'game': 2, 'round': 15, 'date': '2024-06-26', 'venue': 'MCG'},
                    {'game': 3, 'round': 17, 'date': '2024-07-17', 'venue': 'Suncorp Stadium'}
                ],
                'affected_rounds': [13, 14, 15, 16, 17, 18],  # Including preparation/recovery
                'peak_impact_rounds': [14, 15, 17]
            },
            2025: {
                'games': [
                    {'game': 1, 'round': 14, 'date': '2025-06-04', 'venue': 'Suncorp Stadium'},
                    {'game': 2, 'round': 15, 'date': '2025-06-25', 'venue': 'Accor Stadium'},
                    {'game': 3, 'round': 17, 'date': '2025-07-16', 'venue': 'MCG'}
                ],
                'affected_rounds': [13, 14, 15, 16, 17, 18],
                'peak_impact_rounds': [14, 15, 17]
            }
        }
        
        return schedules.get(season, schedules[2025])  # Default to 2025 schedule
    
    def _calculate_origin_round_impact(self, round_num: int, origin_schedule: Dict) -> Dict:
        """Calculate how much a specific round is impacted by Origin"""
        affected_rounds = origin_schedule['affected_rounds']
        peak_rounds = origin_schedule['peak_impact_rounds']
        
        if round_num in peak_rounds:
            return {
                'is_affected': True,
                'intensity': 'High',
                'impact_factor': 0.85,  # 15% reduction
                'reason': 'Direct Origin round'
            }
        elif round_num in affected_rounds:
            # Calculate distance from nearest Origin round
            distances = [abs(round_num - r) for r in peak_rounds]
            min_distance = min(distances)
            
            if min_distance == 1:
                return {
                    'is_affected': True,
                    'intensity': 'Medium',
                    'impact_factor': 0.92,  # 8% reduction
                    'reason': 'Adjacent to Origin round'
                }
            else:
                return {
                    'is_affected': True,
                    'intensity': 'Low',
                    'impact_factor': 0.96,  # 4% reduction
                    'reason': 'Origin preparation/recovery period'
                }
        else:
            return {
                'is_affected': False,
                'intensity': 'None',
                'impact_factor': 1.0,
                'reason': 'No Origin impact'
            }
    
    def _get_team_origin_impact(self, team: str, origin_impact: Dict) -> Dict:
        """Get team-specific Origin impact based on player representation"""
        # Enhanced team Origin profiles
        team_origin_profiles = {
            'Brisbane Broncos': {'qld_players': 5, 'nsw_players': 1, 'total_origin': 6},
            'Gold Coast Titans': {'qld_players': 2, 'nsw_players': 0, 'total_origin': 2},
            'North Queensland Cowboys': {'qld_players': 4, 'nsw_players': 0, 'total_origin': 4},
            'Melbourne Storm': {'qld_players': 2, 'nsw_players': 2, 'total_origin': 4},
            'Canterbury Bulldogs': {'qld_players': 0, 'nsw_players': 3, 'total_origin': 3},
            'Cronulla Sharks': {'qld_players': 1, 'nsw_players': 3, 'total_origin': 4},
            'Manly Sea Eagles': {'qld_players': 0, 'nsw_players': 2, 'total_origin': 2},
            'Newcastle Knights': {'qld_players': 0, 'nsw_players': 2, 'total_origin': 2},
            'Parramatta Eels': {'qld_players': 1, 'nsw_players': 3, 'total_origin': 4},
            'Penrith Panthers': {'qld_players': 1, 'nsw_players': 6, 'total_origin': 7},
            'South Sydney Rabbitohs': {'qld_players': 1, 'nsw_players': 3, 'total_origin': 4},
            'St George Illawarra Dragons': {'qld_players': 0, 'nsw_players': 2, 'total_origin': 2},
            'Sydney Roosters': {'qld_players': 1, 'nsw_players': 4, 'total_origin': 5},
            'Wests Tigers': {'qld_players': 0, 'nsw_players': 1, 'total_origin': 1},
            'Canberra Raiders': {'qld_players': 0, 'nsw_players': 1, 'total_origin': 1},
            'New Zealand Warriors': {'qld_players': 0, 'nsw_players': 0, 'total_origin': 0}
        }
        
        profile = team_origin_profiles.get(team, {'qld_players': 1, 'nsw_players': 1, 'total_origin': 2})
        
        if not origin_impact['is_affected']:
            scoring_factor = 1.0
        else:
            # More Origin players = more impact
            base_impact = origin_impact['impact_factor']
            origin_multiplier = 1.0 - (profile['total_origin'] * 0.02)  # 2% per Origin player
            scoring_factor = base_impact * origin_multiplier
            scoring_factor = max(0.75, scoring_factor)  # Minimum 25% reduction
        
        return {
            'player_count': profile['total_origin'],
            'qld_representation': profile['qld_players'],
            'nsw_representation': profile['nsw_players'],
            'scoring_factor': scoring_factor,
            'impact_level': self._categorize_impact_level(scoring_factor)
        }

    def _get_enhanced_player_profile(self, team: str) -> Dict:
        """Get enhanced player profile with detailed positional analysis"""
        # Comprehensive team profiles with positional strengths
        profiles = {
            'Penrith Panthers': {
                'spine_quality': 9.5,  # Halfback, hooker, fullback quality
                'forward_pack_strength': 9.0,
                'outside_backs_quality': 8.5,
                'attacking_creativity': 9.0,
                'game_management': 9.5,
                'key_positions': {
                    'halfback': {'quality': 10, 'origin_player': True, 'impact': 'Elite'},
                    'hooker': {'quality': 8, 'origin_player': True, 'impact': 'High'},
                    'fullback': {'quality': 9, 'origin_player': False, 'impact': 'High'},
                    'five_eighth': {'quality': 8, 'origin_player': False, 'impact': 'Medium'}
                },
                'scoring_threats': ['structured_attack', 'individual_brilliance', 'set_plays'],
                'weakness_areas': ['defensive_lapses']
            },
            'Melbourne Storm': {
                'spine_quality': 8.5,
                'forward_pack_strength': 8.0,
                'outside_backs_quality': 7.5,
                'attacking_creativity': 8.0,
                'game_management': 9.5,
                'key_positions': {
                    'halfback': {'quality': 8, 'origin_player': False, 'impact': 'High'},
                    'hooker': {'quality': 9, 'origin_player': True, 'impact': 'Elite'},
                    'fullback': {'quality': 8, 'origin_player': True, 'impact': 'High'},
                    'five_eighth': {'quality': 7, 'origin_player': False, 'impact': 'Medium'}
                },
                'scoring_threats': ['structured_attack', 'game_management', 'clutch_plays'],
                'weakness_areas': ['aging_spine']
            },
            'Sydney Roosters': {
                'spine_quality': 8.0,
                'forward_pack_strength': 8.5,
                'outside_backs_quality': 8.0,
                'attacking_creativity': 8.5,
                'game_management': 8.0,
                'key_positions': {
                    'halfback': {'quality': 8, 'origin_player': True, 'impact': 'High'},
                    'hooker': {'quality': 7, 'origin_player': False, 'impact': 'Medium'},
                    'fullback': {'quality': 8, 'origin_player': True, 'impact': 'High'},
                    'five_eighth': {'quality': 8, 'origin_player': True, 'impact': 'High'}
                },
                'scoring_threats': ['attacking_weapons', 'experience', 'big_game_players'],
                'weakness_areas': ['depth_concerns']
            },
            'Brisbane Broncos': {
                'spine_quality': 7.5,
                'forward_pack_strength': 8.0,
                'outside_backs_quality': 8.0,
                'attacking_creativity': 7.5,
                'game_management': 7.0,
                'key_positions': {
                    'halfback': {'quality': 7, 'origin_player': False, 'impact': 'Medium'},
                    'hooker': {'quality': 6, 'origin_player': False, 'impact': 'Low'},
                    'fullback': {'quality': 9, 'origin_player': True, 'impact': 'Elite'},
                    'five_eighth': {'quality': 7, 'origin_player': False, 'impact': 'Medium'}
                },
                'scoring_threats': ['individual_brilliance', 'young_talent'],
                'weakness_areas': ['consistency', 'game_management']
            }
        }

        # Default profile for teams not specifically listed
        default_profile = {
            'spine_quality': 6.5,
            'forward_pack_strength': 6.5,
            'outside_backs_quality': 6.5,
            'attacking_creativity': 6.5,
            'game_management': 6.5,
            'key_positions': {
                'halfback': {'quality': 6, 'origin_player': False, 'impact': 'Medium'},
                'hooker': {'quality': 6, 'origin_player': False, 'impact': 'Medium'},
                'fullback': {'quality': 6, 'origin_player': False, 'impact': 'Medium'},
                'five_eighth': {'quality': 6, 'origin_player': False, 'impact': 'Medium'}
            },
            'scoring_threats': ['standard_attack'],
            'weakness_areas': ['developing_systems']
        }

        return profiles.get(team, default_profile)

    def _calculate_positional_impact(self, player_profile: Dict) -> Dict:
        """Calculate impact of key positions on scoring"""
        try:
            key_positions = player_profile.get('key_positions', {})

            # Weight different positions by their scoring impact
            position_weights = {
                'halfback': 0.3,      # Highest impact on scoring
                'five_eighth': 0.25,  # High creative impact
                'fullback': 0.2,      # High individual impact
                'hooker': 0.15,       # Service and creativity
                'prop': 0.05,         # Platform setting
                'second_row': 0.05    # Support play
            }

            total_impact = 0
            position_breakdown = {}

            for position, weight in position_weights.items():
                if position in key_positions:
                    pos_data = key_positions[position]
                    quality_score = pos_data.get('quality', 6) / 10.0  # Normalize to 0-1
                    origin_bonus = 0.1 if pos_data.get('origin_player', False) else 0

                    position_impact = (quality_score + origin_bonus) * weight
                    total_impact += position_impact

                    position_breakdown[position] = {
                        'quality_score': quality_score,
                        'origin_bonus': origin_bonus,
                        'weighted_impact': position_impact,
                        'impact_rating': pos_data.get('impact', 'Medium')
                    }

            return {
                'total_positional_impact': total_impact,
                'position_breakdown': position_breakdown,
                'spine_strength': player_profile.get('spine_quality', 6.5) / 10.0,
                'forward_strength': player_profile.get('forward_pack_strength', 6.5) / 10.0
            }

        except Exception as e:
            logger.error(f"Error calculating positional impact: {e}")
            return {'total_positional_impact': 0.65, 'position_breakdown': {}}

    def _analyze_scoring_patterns(self, team: str, player_profile: Dict) -> Dict:
        """Analyze how team's player strengths affect scoring patterns"""
        try:
            scoring_threats = player_profile.get('scoring_threats', [])
            spine_quality = player_profile.get('spine_quality', 6.5)
            creativity = player_profile.get('attacking_creativity', 6.5)

            # Analyze scoring pattern tendencies
            patterns = {
                'structured_scoring': 0.5,    # Set plays, organized attack
                'individual_brilliance': 0.3, # Individual player moments
                'opportunistic': 0.2          # Counter-attack, broken play
            }

            # Adjust based on team strengths
            if 'structured_attack' in scoring_threats:
                patterns['structured_scoring'] += 0.2
                patterns['individual_brilliance'] -= 0.1
                patterns['opportunistic'] -= 0.1

            if 'individual_brilliance' in scoring_threats:
                patterns['individual_brilliance'] += 0.2
                patterns['structured_scoring'] -= 0.1
                patterns['opportunistic'] -= 0.1

            if creativity > 8.0:
                patterns['individual_brilliance'] += 0.1
                patterns['opportunistic'] += 0.1
                patterns['structured_scoring'] -= 0.2

            # Normalize to ensure they sum to 1.0
            total = sum(patterns.values())
            patterns = {k: v/total for k, v in patterns.items()}

            return {
                'scoring_patterns': patterns,
                'primary_threat': max(patterns.items(), key=lambda x: x[1])[0],
                'scoring_consistency': spine_quality / 10.0,
                'big_play_potential': creativity / 10.0
            }

        except Exception as e:
            logger.error(f"Error analyzing scoring patterns: {e}")
            return {
                'scoring_patterns': {'structured_scoring': 0.5, 'individual_brilliance': 0.3, 'opportunistic': 0.2},
                'primary_threat': 'structured_scoring',
                'scoring_consistency': 0.65,
                'big_play_potential': 0.65
            }

    def _calculate_team_strength_rating(self, player_profile: Dict, positional_impact: Dict) -> float:
        """Calculate overall team strength rating"""
        try:
            # Combine different strength factors
            spine_rating = player_profile.get('spine_quality', 6.5) / 10.0
            forward_rating = player_profile.get('forward_pack_strength', 6.5) / 10.0
            backs_rating = player_profile.get('outside_backs_quality', 6.5) / 10.0
            creativity_rating = player_profile.get('attacking_creativity', 6.5) / 10.0
            management_rating = player_profile.get('game_management', 6.5) / 10.0

            positional_rating = positional_impact.get('total_positional_impact', 0.65)

            # Weighted combination
            overall_rating = (
                spine_rating * 0.25 +
                forward_rating * 0.15 +
                backs_rating * 0.15 +
                creativity_rating * 0.2 +
                management_rating * 0.15 +
                positional_rating * 0.1
            )

            return min(1.0, max(0.3, overall_rating))  # Clamp between 0.3 and 1.0

        except Exception as e:
            logger.error(f"Error calculating team strength rating: {e}")
            return 0.65

    def _generate_impact_summary(self, player_profile: Dict, strength_rating: float) -> str:
        """Generate human-readable impact summary"""
        try:
            if strength_rating > 0.85:
                level = "Elite"
            elif strength_rating > 0.75:
                level = "High"
            elif strength_rating > 0.65:
                level = "Above Average"
            elif strength_rating > 0.55:
                level = "Average"
            else:
                level = "Below Average"

            threats = player_profile.get('scoring_threats', [])
            weaknesses = player_profile.get('weakness_areas', [])

            summary = f"{level} attacking strength. "

            if threats:
                summary += f"Key strengths: {', '.join(threats)}. "

            if weaknesses:
                summary += f"Areas of concern: {', '.join(weaknesses)}."

            return summary

        except Exception as e:
            logger.error(f"Error generating impact summary: {e}")
            return "Standard NRL team with average attacking capabilities."

    def _get_historical_origin_performance(self, team: str, season: int) -> Dict:
        """Get historical performance during Origin periods"""
        try:
            # This could be enhanced with actual historical data from database
            # For now, return estimated performance based on team profile

            # Teams with fewer Origin players typically perform better during Origin
            origin_profiles = {
                'New Zealand Warriors': {'avg_performance': 1.05, 'consistency': 'High'},
                'Canberra Raiders': {'avg_performance': 1.02, 'consistency': 'Medium'},
                'Melbourne Storm': {'avg_performance': 0.95, 'consistency': 'High'},
                'Penrith Panthers': {'avg_performance': 0.85, 'consistency': 'Medium'},
                'Brisbane Broncos': {'avg_performance': 0.88, 'consistency': 'Low'}
            }

            profile = origin_profiles.get(team, {'avg_performance': 0.92, 'consistency': 'Medium'})

            return {
                'historical_performance_factor': profile['avg_performance'],
                'consistency_rating': profile['consistency'],
                'sample_size': 'Estimated based on Origin representation',
                'trend': 'Stable' if profile['consistency'] == 'High' else 'Variable'
            }

        except Exception as e:
            logger.error(f"Error getting historical Origin performance: {e}")
            return {'historical_performance_factor': 0.92, 'consistency_rating': 'Medium'}

    def _calculate_match_impact(self, home_origin: Dict, away_origin: Dict,
                              home_players: Dict, away_players: Dict, round_num: int) -> Dict:
        """Calculate combined match impact"""
        try:
            # Combine Origin and player impacts
            home_origin_factor = home_origin.get('scoring_impact_factor', 1.0)
            away_origin_factor = away_origin.get('scoring_impact_factor', 1.0)

            home_strength = home_players.get('overall_strength_rating', 0.65)
            away_strength = away_players.get('overall_strength_rating', 0.65)

            # Calculate final scoring factors
            home_scoring_factor = home_origin_factor * (0.8 + home_strength * 0.4)
            away_scoring_factor = away_origin_factor * (0.8 + away_strength * 0.4)

            # Match context adjustments
            if round_num in [14, 15, 17]:  # Direct Origin rounds
                context = "High Origin Impact"
                confidence_adjustment = 0.9  # Lower confidence due to uncertainty
            elif round_num in [13, 16, 18]:  # Adjacent rounds
                context = "Medium Origin Impact"
                confidence_adjustment = 0.95
            else:
                context = "Standard Round"
                confidence_adjustment = 1.0

            return {
                'home_scoring_factor': home_scoring_factor,
                'away_scoring_factor': away_scoring_factor,
                'match_context': context,
                'confidence_adjustment': confidence_adjustment,
                'expected_total_impact': (home_scoring_factor + away_scoring_factor) / 2,
                'competitive_balance': abs(home_scoring_factor - away_scoring_factor)
            }

        except Exception as e:
            logger.error(f"Error calculating match impact: {e}")
            return {
                'home_scoring_factor': 1.0,
                'away_scoring_factor': 1.0,
                'match_context': 'Standard Round',
                'confidence_adjustment': 1.0
            }

    def _generate_betting_recommendations(self, match_impact: Dict) -> List[str]:
        """Generate betting recommendations based on impact analysis"""
        try:
            recommendations = []

            home_factor = match_impact.get('home_scoring_factor', 1.0)
            away_factor = match_impact.get('away_scoring_factor', 1.0)
            context = match_impact.get('match_context', 'Standard Round')

            # Total points recommendations
            avg_factor = (home_factor + away_factor) / 2
            if avg_factor < 0.9:
                recommendations.append("Consider UNDER total points - reduced scoring expected")
            elif avg_factor > 1.1:
                recommendations.append("Consider OVER total points - enhanced scoring expected")

            # Margin recommendations
            factor_diff = abs(home_factor - away_factor)
            if factor_diff > 0.15:
                stronger_team = "Home" if home_factor > away_factor else "Away"
                recommendations.append(f"Consider {stronger_team} team margin bets - significant strength difference")

            # Origin-specific recommendations
            if "Origin" in context:
                recommendations.append("Lower confidence in predictions - Origin period uncertainty")
                recommendations.append("Consider defensive/low-scoring game scenarios")

            return recommendations

        except Exception as e:
            logger.error(f"Error generating betting recommendations: {e}")
            return ["Standard betting approach recommended"]

    # Default/fallback methods
    def _get_default_origin_impact(self) -> Dict:
        """Default Origin impact when analysis fails"""
        return {
            'is_origin_affected': False,
            'origin_intensity': 'None',
            'team_origin_players': 2,
            'scoring_impact_factor': 1.0,
            'historical_performance': {'historical_performance_factor': 1.0}
        }

    def _get_default_player_analysis(self) -> Dict:
        """Default player analysis when analysis fails"""
        return {
            'overall_strength_rating': 0.65,
            'positional_strengths': {'total_positional_impact': 0.65},
            'scoring_patterns': {'primary_threat': 'structured_scoring'},
            'impact_summary': 'Standard NRL team with average capabilities'
        }

    def _get_default_combined_impact(self) -> Dict:
        """Default combined impact when analysis fails"""
        return {
            'home_team_impact': {'combined_scoring_factor': 1.0},
            'away_team_impact': {'combined_scoring_factor': 1.0},
            'match_analysis': {'match_context': 'Standard Round'},
            'recommendations': ['Standard betting approach recommended']
        }

    def _categorize_impact_level(self, scoring_factor: float) -> str:
        """Categorize impact level based on scoring factor"""
        if scoring_factor >= 0.95:
            return "Minimal"
        elif scoring_factor >= 0.90:
            return "Low"
        elif scoring_factor >= 0.85:
            return "Medium"
        elif scoring_factor >= 0.80:
            return "High"
        else:
            return "Severe"
