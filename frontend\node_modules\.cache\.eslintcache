[{"C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\Predictions.tsx": "4", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\Matches.tsx": "5", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\Dashboard.tsx": "6", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\Teams.tsx": "7", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\LiveScores.tsx": "8", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\components\\common\\Header.tsx": "9", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\components\\common\\Sidebar.tsx": "10", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\components\\common\\Footer.tsx": "11", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\services\\api.ts": "12", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\index.tsx": "13", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\reportWebVitals.ts": "14", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\App.tsx": "15", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\Dashboard.tsx": "16", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\Predictions.tsx": "17", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\Matches.tsx": "18", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\LiveScores.tsx": "19", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\Teams.tsx": "20", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\Header.tsx": "21", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\Sidebar.tsx": "22", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\Footer.tsx": "23", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\services\\api.ts": "24", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\SectionHeader.tsx": "25", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\StatusBadge.tsx": "26", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\ApiCard.tsx": "27", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\CodeBlock.tsx": "28", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\GradientCard.tsx": "29", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\AnimatedProgress.tsx": "30", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\contexts\\ThemeContext.tsx": "31", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\ThemeSelector.tsx": "32", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\TeamCard.tsx": "33", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\utils\\teamStyling.ts": "34", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\EnhancedPredictions.tsx": "35", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\BettingPredictions.tsx": "36", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\OptimizedLoader.tsx": "37", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\utils\\performanceTest.ts": "38", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\utils\\rateLimiter.ts": "39", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\RugbyProgressBar.tsx": "40"}, {"size": 555, "mtime": 1749473774261, "results": "41", "hashOfConfig": "42"}, {"size": 425, "mtime": 1749473801007, "results": "43", "hashOfConfig": "42"}, {"size": 3557, "mtime": 1749473761877, "results": "44", "hashOfConfig": "42"}, {"size": 15879, "mtime": 1749476670646, "results": "45", "hashOfConfig": "42"}, {"size": 9717, "mtime": 1749476644721, "results": "46", "hashOfConfig": "42"}, {"size": 12345, "mtime": 1749476604636, "results": "47", "hashOfConfig": "42"}, {"size": 9247, "mtime": 1749476682018, "results": "48", "hashOfConfig": "42"}, {"size": 12554, "mtime": 1749476616944, "results": "49", "hashOfConfig": "42"}, {"size": 3422, "mtime": 1749473885611, "results": "50", "hashOfConfig": "42"}, {"size": 6805, "mtime": 1749473917764, "results": "51", "hashOfConfig": "42"}, {"size": 6129, "mtime": 1749473945969, "results": "52", "hashOfConfig": "42"}, {"size": 5650, "mtime": 1749473829607, "results": "53", "hashOfConfig": "42"}, {"size": 555, "mtime": 1749473774261, "results": "54", "hashOfConfig": "55"}, {"size": 425, "mtime": 1749473801007, "results": "56", "hashOfConfig": "55"}, {"size": 5345, "mtime": 1749535823571, "results": "57", "hashOfConfig": "55"}, {"size": 22691, "mtime": 1749531392999, "results": "58", "hashOfConfig": "55"}, {"size": 25114, "mtime": 1749501879350, "results": "59", "hashOfConfig": "55"}, {"size": 11675, "mtime": 1749531380725, "results": "60", "hashOfConfig": "55"}, {"size": 15406, "mtime": 1749531521532, "results": "61", "hashOfConfig": "55"}, {"size": 6480, "mtime": 1749519279331, "results": "62", "hashOfConfig": "55"}, {"size": 7781, "mtime": 1749531304985, "results": "63", "hashOfConfig": "55"}, {"size": 6989, "mtime": 1749506846670, "results": "64", "hashOfConfig": "55"}, {"size": 6129, "mtime": 1749473945969, "results": "65", "hashOfConfig": "55"}, {"size": 12813, "mtime": 1749532684079, "results": "66", "hashOfConfig": "55"}, {"size": 5068, "mtime": 1749485714527, "results": "67", "hashOfConfig": "55"}, {"size": 3408, "mtime": 1749531349454, "results": "68", "hashOfConfig": "55"}, {"size": 7090, "mtime": 1749485648767, "results": "69", "hashOfConfig": "55"}, {"size": 5123, "mtime": 1749485691157, "results": "70", "hashOfConfig": "55"}, {"size": 4085, "mtime": 1749486719196, "results": "71", "hashOfConfig": "55"}, {"size": 3319, "mtime": 1749486739811, "results": "72", "hashOfConfig": "55"}, {"size": 11498, "mtime": 1749493200070, "results": "73", "hashOfConfig": "55"}, {"size": 8547, "mtime": 1749488217299, "results": "74", "hashOfConfig": "55"}, {"size": 11550, "mtime": 1749491572609, "results": "75", "hashOfConfig": "55"}, {"size": 12438, "mtime": 1749491910902, "results": "76", "hashOfConfig": "55"}, {"size": 26065, "mtime": 1749526831318, "results": "77", "hashOfConfig": "55"}, {"size": 39028, "mtime": 1749533594578, "results": "78", "hashOfConfig": "55"}, {"size": 3665, "mtime": 1749526860894, "results": "79", "hashOfConfig": "55"}, {"size": 6434, "mtime": 1749509821675, "results": "80", "hashOfConfig": "55"}, {"size": 2256, "mtime": 1749510935115, "results": "81", "hashOfConfig": "55"}, {"size": 14858, "mtime": 1749532965153, "results": "82", "hashOfConfig": "55"}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kglup0", {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mkt1cb", {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\Predictions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\Matches.tsx", ["203", "204"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\Dashboard.tsx", ["205", "206"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\Teams.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\LiveScores.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\components\\common\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\components\\common\\Sidebar.tsx", ["207", "208"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\components\\common\\Footer.tsx", ["209"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\App.tsx", ["210", "211"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\Dashboard.tsx", ["212", "213", "214", "215", "216", "217", "218"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\Predictions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\Matches.tsx", ["219", "220", "221", "222", "223", "224"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\LiveScores.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\Teams.tsx", ["225", "226", "227", "228", "229"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\Sidebar.tsx", ["230", "231"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\Footer.tsx", ["232"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\services\\api.ts", ["233", "234"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\SectionHeader.tsx", ["235"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\StatusBadge.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\ApiCard.tsx", ["236"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\CodeBlock.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\GradientCard.tsx", ["237"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\AnimatedProgress.tsx", ["238"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\ThemeSelector.tsx", ["239"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\TeamCard.tsx", ["240", "241"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\utils\\teamStyling.ts", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\EnhancedPredictions.tsx", ["242", "243", "244", "245", "246", "247", "248", "249"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\BettingPredictions.tsx", ["250", "251", "252", "253", "254", "255"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\OptimizedLoader.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\utils\\performanceTest.ts", ["256"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\utils\\rateLimiter.ts", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\RugbyProgressBar.tsx", [], [], {"ruleId": "257", "severity": 1, "message": "258", "line": 22, "column": 3, "nodeType": "259", "messageId": "260", "endLine": 22, "endColumn": 9}, {"ruleId": "257", "severity": 1, "message": "261", "line": 27, "column": 21, "nodeType": "259", "messageId": "260", "endLine": 27, "endColumn": 30}, {"ruleId": "257", "severity": 1, "message": "262", "line": 20, "column": 17, "nodeType": "259", "messageId": "260", "endLine": 20, "endColumn": 31}, {"ruleId": "257", "severity": 1, "message": "263", "line": 46, "column": 17, "nodeType": "259", "messageId": "260", "endLine": 46, "endColumn": 26}, {"ruleId": "257", "severity": 1, "message": "264", "line": 24, "column": 16, "nodeType": "259", "messageId": "260", "endLine": 24, "endColumn": 29}, {"ruleId": "257", "severity": 1, "message": "265", "line": 25, "column": 17, "nodeType": "259", "messageId": "260", "endLine": 25, "endColumn": 29}, {"ruleId": "257", "severity": 1, "message": "266", "line": 12, "column": 13, "nodeType": "259", "messageId": "260", "endLine": 12, "endColumn": 23}, {"ruleId": "257", "severity": 1, "message": "267", "line": 3, "column": 28, "nodeType": "259", "messageId": "260", "endLine": 3, "endColumn": 44}, {"ruleId": "257", "severity": 1, "message": "268", "line": 3, "column": 46, "nodeType": "259", "messageId": "260", "endLine": 3, "endColumn": 56}, {"ruleId": "257", "severity": 1, "message": "269", "line": 1, "column": 17, "nodeType": "259", "messageId": "260", "endLine": 1, "endColumn": 26}, {"ruleId": "257", "severity": 1, "message": "270", "line": 10, "column": 3, "nodeType": "259", "messageId": "260", "endLine": 10, "endColumn": 17}, {"ruleId": "257", "severity": 1, "message": "262", "line": 20, "column": 17, "nodeType": "259", "messageId": "260", "endLine": 20, "endColumn": 31}, {"ruleId": "257", "severity": 1, "message": "271", "line": 28, "column": 14, "nodeType": "259", "messageId": "260", "endLine": 28, "endColumn": 25}, {"ruleId": "257", "severity": 1, "message": "272", "line": 29, "column": 11, "nodeType": "259", "messageId": "260", "endLine": 29, "endColumn": 19}, {"ruleId": "257", "severity": 1, "message": "273", "line": 50, "column": 36, "nodeType": "259", "messageId": "260", "endLine": 50, "endColumn": 47}, {"ruleId": "257", "severity": 1, "message": "274", "line": 50, "column": 60, "nodeType": "259", "messageId": "260", "endLine": 50, "endColumn": 73}, {"ruleId": "257", "severity": 1, "message": "258", "line": 22, "column": 3, "nodeType": "259", "messageId": "260", "endLine": 22, "endColumn": 9}, {"ruleId": "257", "severity": 1, "message": "261", "line": 27, "column": 21, "nodeType": "259", "messageId": "260", "endLine": 27, "endColumn": 30}, {"ruleId": "257", "severity": 1, "message": "271", "line": 31, "column": 14, "nodeType": "259", "messageId": "260", "endLine": 31, "endColumn": 25}, {"ruleId": "257", "severity": 1, "message": "272", "line": 32, "column": 11, "nodeType": "259", "messageId": "260", "endLine": 32, "endColumn": 19}, {"ruleId": "257", "severity": 1, "message": "275", "line": 43, "column": 11, "nodeType": "259", "messageId": "260", "endLine": 43, "endColumn": 20}, {"ruleId": "257", "severity": 1, "message": "276", "line": 70, "column": 37, "nodeType": "259", "messageId": "260", "endLine": 70, "endColumn": 49}, {"ruleId": "257", "severity": 1, "message": "258", "line": 8, "column": 3, "nodeType": "259", "messageId": "260", "endLine": 8, "endColumn": 9}, {"ruleId": "257", "severity": 1, "message": "277", "line": 9, "column": 3, "nodeType": "259", "messageId": "260", "endLine": 9, "endColumn": 7}, {"ruleId": "257", "severity": 1, "message": "270", "line": 10, "column": 3, "nodeType": "259", "messageId": "260", "endLine": 10, "endColumn": 17}, {"ruleId": "257", "severity": 1, "message": "272", "line": 18, "column": 11, "nodeType": "259", "messageId": "260", "endLine": 18, "endColumn": 19}, {"ruleId": "257", "severity": 1, "message": "275", "line": 29, "column": 11, "nodeType": "259", "messageId": "260", "endLine": 29, "endColumn": 20}, {"ruleId": "257", "severity": 1, "message": "264", "line": 24, "column": 16, "nodeType": "259", "messageId": "260", "endLine": 24, "endColumn": 29}, {"ruleId": "257", "severity": 1, "message": "265", "line": 25, "column": 17, "nodeType": "259", "messageId": "260", "endLine": 25, "endColumn": 29}, {"ruleId": "257", "severity": 1, "message": "266", "line": 12, "column": 13, "nodeType": "259", "messageId": "260", "endLine": 12, "endColumn": 23}, {"ruleId": "257", "severity": 1, "message": "278", "line": 2, "column": 10, "nodeType": "259", "messageId": "260", "endLine": 2, "endColumn": 28}, {"ruleId": "257", "severity": 1, "message": "279", "line": 3, "column": 53, "nodeType": "259", "messageId": "260", "endLine": 3, "endColumn": 71}, {"ruleId": "257", "severity": 1, "message": "280", "line": 5, "column": 3, "nodeType": "259", "messageId": "260", "endLine": 5, "endColumn": 10}, {"ruleId": "257", "severity": 1, "message": "281", "line": 16, "column": 11, "nodeType": "259", "messageId": "260", "endLine": 16, "endColumn": 19}, {"ruleId": "257", "severity": 1, "message": "282", "line": 106, "column": 9, "nodeType": "259", "messageId": "260", "endLine": 106, "endColumn": 14}, {"ruleId": "257", "severity": 1, "message": "282", "line": 75, "column": 9, "nodeType": "259", "messageId": "260", "endLine": 75, "endColumn": 14}, {"ruleId": "257", "severity": 1, "message": "283", "line": 241, "column": 11, "nodeType": "259", "messageId": "260", "endLine": 241, "endColumn": 23}, {"ruleId": "257", "severity": 1, "message": "258", "line": 7, "column": 3, "nodeType": "259", "messageId": "260", "endLine": 7, "endColumn": 9}, {"ruleId": "257", "severity": 1, "message": "284", "line": 53, "column": 9, "nodeType": "259", "messageId": "260", "endLine": 53, "endColumn": 21}, {"ruleId": "257", "severity": 1, "message": "267", "line": 16, "column": 3, "nodeType": "259", "messageId": "260", "endLine": 16, "endColumn": 19}, {"ruleId": "257", "severity": 1, "message": "285", "line": 31, "column": 3, "nodeType": "259", "messageId": "260", "endLine": 31, "endColumn": 12}, {"ruleId": "257", "severity": 1, "message": "286", "line": 32, "column": 3, "nodeType": "259", "messageId": "260", "endLine": 32, "endColumn": 19}, {"ruleId": "257", "severity": 1, "message": "287", "line": 33, "column": 3, "nodeType": "259", "messageId": "260", "endLine": 33, "endColumn": 19}, {"ruleId": "257", "severity": 1, "message": "288", "line": 41, "column": 17, "nodeType": "259", "messageId": "260", "endLine": 41, "endColumn": 31}, {"ruleId": "257", "severity": 1, "message": "289", "line": 48, "column": 8, "nodeType": "259", "messageId": "260", "endLine": 48, "endColumn": 23}, {"ruleId": "257", "severity": 1, "message": "290", "line": 50, "column": 10, "nodeType": "259", "messageId": "260", "endLine": 50, "endColumn": 24}, {"ruleId": "257", "severity": 1, "message": "291", "line": 50, "column": 26, "nodeType": "259", "messageId": "260", "endLine": 50, "endColumn": 43}, {"ruleId": "257", "severity": 1, "message": "267", "line": 16, "column": 3, "nodeType": "259", "messageId": "260", "endLine": 16, "endColumn": 19}, {"ruleId": "257", "severity": 1, "message": "280", "line": 34, "column": 3, "nodeType": "259", "messageId": "260", "endLine": 34, "endColumn": 10}, {"ruleId": "257", "severity": 1, "message": "264", "line": 38, "column": 17, "nodeType": "259", "messageId": "260", "endLine": 38, "endColumn": 30}, {"ruleId": "257", "severity": 1, "message": "292", "line": 41, "column": 13, "nodeType": "259", "messageId": "260", "endLine": 41, "endColumn": 23}, {"ruleId": "257", "severity": 1, "message": "293", "line": 100, "column": 105, "nodeType": "259", "messageId": "260", "endLine": 100, "endColumn": 125}, {"ruleId": "257", "severity": 1, "message": "294", "line": 114, "column": 93, "nodeType": "259", "messageId": "260", "endLine": 114, "endColumn": 109}, {"ruleId": "257", "severity": 1, "message": "295", "line": 95, "column": 9, "nodeType": "259", "messageId": "260", "endLine": 95, "endColumn": 18}, "@typescript-eslint/no-unused-vars", "'Avatar' is defined but never used.", "Identifier", "unusedVar", "'MatchIcon' is defined but never used.", "'TrendingUpIcon' is defined but never used.", "'teamsData' is assigned a value but never used.", "'AnalyticsIcon' is defined but never used.", "'TrendingIcon' is defined but never used.", "'GitHubIcon' is defined but never used.", "'CircularProgress' is defined but never used.", "'Typography' is defined but never used.", "'useEffect' is defined but never used.", "'LinearProgress' is defined but never used.", "'RefreshIcon' is defined but never used.", "'InfoIcon' is defined but never used.", "'healthError' is assigned a value but never used.", "'healthLoading' is assigned a value but never used.", "'gradients' is assigned a value but never used.", "'refetchTeams' is assigned a value but never used.", "'Chip' is defined but never used.", "'performanceService' is defined but never used.", "'generalRateLimiter' is defined but never used.", "'Divider' is defined but never used.", "'CodeIcon' is defined but never used.", "'theme' is assigned a value but never used.", "'currentTheme' is assigned a value but never used.", "'teamGradient' is assigned a value but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "'OptimizedLoader' is defined but never used.", "'usePerformance' is defined but never used.", "'useOptimizedQuery' is defined but never used.", "'SportsIcon' is defined but never used.", "'refetchOpportunities' is assigned a value but never used.", "'refetch<PERSON><PERSON><PERSON><PERSON>' is assigned a value but never used.", "'totalSize' is assigned a value but never used."]