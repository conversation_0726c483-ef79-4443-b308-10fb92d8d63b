[{"C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\Predictions.tsx": "4", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\Matches.tsx": "5", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\Dashboard.tsx": "6", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\Teams.tsx": "7", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\LiveScores.tsx": "8", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\components\\common\\Header.tsx": "9", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\components\\common\\Sidebar.tsx": "10", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\components\\common\\Footer.tsx": "11", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\services\\api.ts": "12", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\index.tsx": "13", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\reportWebVitals.ts": "14", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\App.tsx": "15", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\Dashboard.tsx": "16", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\Predictions.tsx": "17", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\Matches.tsx": "18", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\LiveScores.tsx": "19", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\Teams.tsx": "20", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\Header.tsx": "21", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\Sidebar.tsx": "22", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\Footer.tsx": "23", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\services\\api.ts": "24", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\SectionHeader.tsx": "25", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\StatusBadge.tsx": "26", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\ApiCard.tsx": "27", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\CodeBlock.tsx": "28", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\GradientCard.tsx": "29", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\AnimatedProgress.tsx": "30", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\contexts\\ThemeContext.tsx": "31", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\ThemeSelector.tsx": "32", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\TeamCard.tsx": "33", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\utils\\teamStyling.ts": "34", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\EnhancedPredictions.tsx": "35", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\BettingPredictions.tsx": "36", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\OptimizedLoader.tsx": "37", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\utils\\performanceTest.ts": "38", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\utils\\rateLimiter.ts": "39", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\RugbyProgressBar.tsx": "40", "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\layout\\PageWrapper.tsx": "41"}, {"size": 555, "mtime": 1749473774261, "results": "42", "hashOfConfig": "43"}, {"size": 425, "mtime": 1749473801007, "results": "44", "hashOfConfig": "43"}, {"size": 3557, "mtime": 1749473761877, "results": "45", "hashOfConfig": "43"}, {"size": 15879, "mtime": 1749476670646, "results": "46", "hashOfConfig": "43"}, {"size": 9717, "mtime": 1749476644721, "results": "47", "hashOfConfig": "43"}, {"size": 12345, "mtime": 1749476604636, "results": "48", "hashOfConfig": "43"}, {"size": 9247, "mtime": 1749476682018, "results": "49", "hashOfConfig": "43"}, {"size": 12554, "mtime": 1749476616944, "results": "50", "hashOfConfig": "43"}, {"size": 3422, "mtime": 1749473885611, "results": "51", "hashOfConfig": "43"}, {"size": 6805, "mtime": 1749473917764, "results": "52", "hashOfConfig": "43"}, {"size": 6129, "mtime": 1749473945969, "results": "53", "hashOfConfig": "43"}, {"size": 5650, "mtime": 1749473829607, "results": "54", "hashOfConfig": "43"}, {"size": 555, "mtime": 1749473774261, "results": "55", "hashOfConfig": "56"}, {"size": 425, "mtime": 1749473801007, "results": "57", "hashOfConfig": "56"}, {"size": 6071, "mtime": 1749536632564, "results": "58", "hashOfConfig": "56"}, {"size": 22744, "mtime": 1749536673511, "results": "59", "hashOfConfig": "56"}, {"size": 25114, "mtime": 1749501879350, "results": "60", "hashOfConfig": "56"}, {"size": 11675, "mtime": 1749531380725, "results": "61", "hashOfConfig": "56"}, {"size": 15406, "mtime": 1749531521532, "results": "62", "hashOfConfig": "56"}, {"size": 6480, "mtime": 1749519279331, "results": "63", "hashOfConfig": "56"}, {"size": 7781, "mtime": 1749531304985, "results": "64", "hashOfConfig": "56"}, {"size": 7558, "mtime": 1749536574688, "results": "65", "hashOfConfig": "56"}, {"size": 6129, "mtime": 1749473945969, "results": "66", "hashOfConfig": "56"}, {"size": 12813, "mtime": 1749532684079, "results": "67", "hashOfConfig": "56"}, {"size": 5068, "mtime": 1749485714527, "results": "68", "hashOfConfig": "56"}, {"size": 3408, "mtime": 1749531349454, "results": "69", "hashOfConfig": "56"}, {"size": 7090, "mtime": 1749485648767, "results": "70", "hashOfConfig": "56"}, {"size": 5123, "mtime": 1749485691157, "results": "71", "hashOfConfig": "56"}, {"size": 4085, "mtime": 1749486719196, "results": "72", "hashOfConfig": "56"}, {"size": 3319, "mtime": 1749486739811, "results": "73", "hashOfConfig": "56"}, {"size": 11498, "mtime": 1749493200070, "results": "74", "hashOfConfig": "56"}, {"size": 8547, "mtime": 1749488217299, "results": "75", "hashOfConfig": "56"}, {"size": 11550, "mtime": 1749491572609, "results": "76", "hashOfConfig": "56"}, {"size": 12438, "mtime": 1749491910902, "results": "77", "hashOfConfig": "56"}, {"size": 26065, "mtime": 1749526831318, "results": "78", "hashOfConfig": "56"}, {"size": 39028, "mtime": 1749533594578, "results": "79", "hashOfConfig": "56"}, {"size": 3665, "mtime": 1749526860894, "results": "80", "hashOfConfig": "56"}, {"size": 6434, "mtime": 1749509821675, "results": "81", "hashOfConfig": "56"}, {"size": 2256, "mtime": 1749510935115, "results": "82", "hashOfConfig": "56"}, {"size": 14858, "mtime": 1749532965153, "results": "83", "hashOfConfig": "56"}, {"size": 1071, "mtime": 1749536612410, "results": "84", "hashOfConfig": "56"}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kglup0", {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mkt1cb", {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\Predictions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\Matches.tsx", ["208", "209"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\Dashboard.tsx", ["210", "211"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\Teams.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\pages\\LiveScores.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\components\\common\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\components\\common\\Sidebar.tsx", ["212", "213"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\components\\common\\Footer.tsx", ["214"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\prediction_pro_v4\\nrl_predictor_v5\\frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\App.tsx", ["215", "216"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\Dashboard.tsx", ["217", "218", "219", "220", "221", "222", "223"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\Predictions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\Matches.tsx", ["224", "225", "226", "227", "228", "229"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\LiveScores.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\Teams.tsx", ["230", "231", "232", "233", "234"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\Sidebar.tsx", ["235", "236"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\Footer.tsx", ["237"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\services\\api.ts", ["238", "239"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\SectionHeader.tsx", ["240"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\StatusBadge.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\ApiCard.tsx", ["241"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\CodeBlock.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\GradientCard.tsx", ["242"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\AnimatedProgress.tsx", ["243"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\ThemeSelector.tsx", ["244"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\TeamCard.tsx", ["245", "246"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\utils\\teamStyling.ts", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\EnhancedPredictions.tsx", ["247", "248", "249", "250", "251", "252", "253", "254"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\pages\\BettingPredictions.tsx", ["255", "256", "257", "258", "259", "260"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\OptimizedLoader.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\utils\\performanceTest.ts", ["261"], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\utils\\rateLimiter.ts", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\common\\RugbyProgressBar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Personal\\Sources\\nrl_predictor_v5\\frontend\\src\\components\\layout\\PageWrapper.tsx", [], [], {"ruleId": "262", "severity": 1, "message": "263", "line": 22, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 22, "endColumn": 9}, {"ruleId": "262", "severity": 1, "message": "266", "line": 27, "column": 21, "nodeType": "264", "messageId": "265", "endLine": 27, "endColumn": 30}, {"ruleId": "262", "severity": 1, "message": "267", "line": 20, "column": 17, "nodeType": "264", "messageId": "265", "endLine": 20, "endColumn": 31}, {"ruleId": "262", "severity": 1, "message": "268", "line": 46, "column": 17, "nodeType": "264", "messageId": "265", "endLine": 46, "endColumn": 26}, {"ruleId": "262", "severity": 1, "message": "269", "line": 24, "column": 16, "nodeType": "264", "messageId": "265", "endLine": 24, "endColumn": 29}, {"ruleId": "262", "severity": 1, "message": "270", "line": 25, "column": 17, "nodeType": "264", "messageId": "265", "endLine": 25, "endColumn": 29}, {"ruleId": "262", "severity": 1, "message": "271", "line": 12, "column": 13, "nodeType": "264", "messageId": "265", "endLine": 12, "endColumn": 23}, {"ruleId": "262", "severity": 1, "message": "272", "line": 3, "column": 28, "nodeType": "264", "messageId": "265", "endLine": 3, "endColumn": 44}, {"ruleId": "262", "severity": 1, "message": "273", "line": 3, "column": 46, "nodeType": "264", "messageId": "265", "endLine": 3, "endColumn": 56}, {"ruleId": "262", "severity": 1, "message": "274", "line": 1, "column": 17, "nodeType": "264", "messageId": "265", "endLine": 1, "endColumn": 26}, {"ruleId": "262", "severity": 1, "message": "275", "line": 10, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 10, "endColumn": 17}, {"ruleId": "262", "severity": 1, "message": "267", "line": 20, "column": 17, "nodeType": "264", "messageId": "265", "endLine": 20, "endColumn": 31}, {"ruleId": "262", "severity": 1, "message": "276", "line": 28, "column": 14, "nodeType": "264", "messageId": "265", "endLine": 28, "endColumn": 25}, {"ruleId": "262", "severity": 1, "message": "277", "line": 29, "column": 11, "nodeType": "264", "messageId": "265", "endLine": 29, "endColumn": 19}, {"ruleId": "262", "severity": 1, "message": "278", "line": 51, "column": 36, "nodeType": "264", "messageId": "265", "endLine": 51, "endColumn": 47}, {"ruleId": "262", "severity": 1, "message": "279", "line": 51, "column": 60, "nodeType": "264", "messageId": "265", "endLine": 51, "endColumn": 73}, {"ruleId": "262", "severity": 1, "message": "263", "line": 22, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 22, "endColumn": 9}, {"ruleId": "262", "severity": 1, "message": "266", "line": 27, "column": 21, "nodeType": "264", "messageId": "265", "endLine": 27, "endColumn": 30}, {"ruleId": "262", "severity": 1, "message": "276", "line": 31, "column": 14, "nodeType": "264", "messageId": "265", "endLine": 31, "endColumn": 25}, {"ruleId": "262", "severity": 1, "message": "277", "line": 32, "column": 11, "nodeType": "264", "messageId": "265", "endLine": 32, "endColumn": 19}, {"ruleId": "262", "severity": 1, "message": "280", "line": 43, "column": 11, "nodeType": "264", "messageId": "265", "endLine": 43, "endColumn": 20}, {"ruleId": "262", "severity": 1, "message": "281", "line": 70, "column": 37, "nodeType": "264", "messageId": "265", "endLine": 70, "endColumn": 49}, {"ruleId": "262", "severity": 1, "message": "263", "line": 8, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 8, "endColumn": 9}, {"ruleId": "262", "severity": 1, "message": "282", "line": 9, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 9, "endColumn": 7}, {"ruleId": "262", "severity": 1, "message": "275", "line": 10, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 10, "endColumn": 17}, {"ruleId": "262", "severity": 1, "message": "277", "line": 18, "column": 11, "nodeType": "264", "messageId": "265", "endLine": 18, "endColumn": 19}, {"ruleId": "262", "severity": 1, "message": "280", "line": 29, "column": 11, "nodeType": "264", "messageId": "265", "endLine": 29, "endColumn": 20}, {"ruleId": "262", "severity": 1, "message": "269", "line": 24, "column": 16, "nodeType": "264", "messageId": "265", "endLine": 24, "endColumn": 29}, {"ruleId": "262", "severity": 1, "message": "270", "line": 25, "column": 17, "nodeType": "264", "messageId": "265", "endLine": 25, "endColumn": 29}, {"ruleId": "262", "severity": 1, "message": "271", "line": 12, "column": 13, "nodeType": "264", "messageId": "265", "endLine": 12, "endColumn": 23}, {"ruleId": "262", "severity": 1, "message": "283", "line": 2, "column": 10, "nodeType": "264", "messageId": "265", "endLine": 2, "endColumn": 28}, {"ruleId": "262", "severity": 1, "message": "284", "line": 3, "column": 53, "nodeType": "264", "messageId": "265", "endLine": 3, "endColumn": 71}, {"ruleId": "262", "severity": 1, "message": "285", "line": 5, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 5, "endColumn": 10}, {"ruleId": "262", "severity": 1, "message": "286", "line": 16, "column": 11, "nodeType": "264", "messageId": "265", "endLine": 16, "endColumn": 19}, {"ruleId": "262", "severity": 1, "message": "287", "line": 106, "column": 9, "nodeType": "264", "messageId": "265", "endLine": 106, "endColumn": 14}, {"ruleId": "262", "severity": 1, "message": "287", "line": 75, "column": 9, "nodeType": "264", "messageId": "265", "endLine": 75, "endColumn": 14}, {"ruleId": "262", "severity": 1, "message": "288", "line": 241, "column": 11, "nodeType": "264", "messageId": "265", "endLine": 241, "endColumn": 23}, {"ruleId": "262", "severity": 1, "message": "263", "line": 7, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 7, "endColumn": 9}, {"ruleId": "262", "severity": 1, "message": "289", "line": 53, "column": 9, "nodeType": "264", "messageId": "265", "endLine": 53, "endColumn": 21}, {"ruleId": "262", "severity": 1, "message": "272", "line": 16, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 16, "endColumn": 19}, {"ruleId": "262", "severity": 1, "message": "290", "line": 31, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 31, "endColumn": 12}, {"ruleId": "262", "severity": 1, "message": "291", "line": 32, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 32, "endColumn": 19}, {"ruleId": "262", "severity": 1, "message": "292", "line": 33, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 33, "endColumn": 19}, {"ruleId": "262", "severity": 1, "message": "293", "line": 41, "column": 17, "nodeType": "264", "messageId": "265", "endLine": 41, "endColumn": 31}, {"ruleId": "262", "severity": 1, "message": "294", "line": 48, "column": 8, "nodeType": "264", "messageId": "265", "endLine": 48, "endColumn": 23}, {"ruleId": "262", "severity": 1, "message": "295", "line": 50, "column": 10, "nodeType": "264", "messageId": "265", "endLine": 50, "endColumn": 24}, {"ruleId": "262", "severity": 1, "message": "296", "line": 50, "column": 26, "nodeType": "264", "messageId": "265", "endLine": 50, "endColumn": 43}, {"ruleId": "262", "severity": 1, "message": "272", "line": 16, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 16, "endColumn": 19}, {"ruleId": "262", "severity": 1, "message": "285", "line": 34, "column": 3, "nodeType": "264", "messageId": "265", "endLine": 34, "endColumn": 10}, {"ruleId": "262", "severity": 1, "message": "269", "line": 38, "column": 17, "nodeType": "264", "messageId": "265", "endLine": 38, "endColumn": 30}, {"ruleId": "262", "severity": 1, "message": "297", "line": 41, "column": 13, "nodeType": "264", "messageId": "265", "endLine": 41, "endColumn": 23}, {"ruleId": "262", "severity": 1, "message": "298", "line": 100, "column": 105, "nodeType": "264", "messageId": "265", "endLine": 100, "endColumn": 125}, {"ruleId": "262", "severity": 1, "message": "299", "line": 114, "column": 93, "nodeType": "264", "messageId": "265", "endLine": 114, "endColumn": 109}, {"ruleId": "262", "severity": 1, "message": "300", "line": 95, "column": 9, "nodeType": "264", "messageId": "265", "endLine": 95, "endColumn": 18}, "@typescript-eslint/no-unused-vars", "'Avatar' is defined but never used.", "Identifier", "unusedVar", "'MatchIcon' is defined but never used.", "'TrendingUpIcon' is defined but never used.", "'teamsData' is assigned a value but never used.", "'AnalyticsIcon' is defined but never used.", "'TrendingIcon' is defined but never used.", "'GitHubIcon' is defined but never used.", "'CircularProgress' is defined but never used.", "'Typography' is defined but never used.", "'useEffect' is defined but never used.", "'LinearProgress' is defined but never used.", "'RefreshIcon' is defined but never used.", "'InfoIcon' is defined but never used.", "'healthError' is assigned a value but never used.", "'healthLoading' is assigned a value but never used.", "'gradients' is assigned a value but never used.", "'refetchTeams' is assigned a value but never used.", "'Chip' is defined but never used.", "'performanceService' is defined but never used.", "'generalRateLimiter' is defined but never used.", "'Divider' is defined but never used.", "'CodeIcon' is defined but never used.", "'theme' is assigned a value but never used.", "'currentTheme' is assigned a value but never used.", "'teamGradient' is assigned a value but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "'OptimizedLoader' is defined but never used.", "'usePerformance' is defined but never used.", "'useOptimizedQuery' is defined but never used.", "'SportsIcon' is defined but never used.", "'refetchOpportunities' is assigned a value but never used.", "'refetch<PERSON><PERSON><PERSON><PERSON>' is assigned a value but never used.", "'totalSize' is assigned a value but never used."]