import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Divider,
  Box,
  Typography,
  Badge,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  SportsFootball as MatchesIcon,
  Groups as TeamsIcon,
  Psychology as PredictionsIcon,
  LiveTv as LiveIcon,
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingIcon,
  MonetizationOn as BettingIcon,
} from '@mui/icons-material';

interface SidebarProps {
  open: boolean;
  onClose: () => void;
}

interface NavigationItem {
  label: string;
  path: string;
  icon: React.ComponentType;
  badge?: number;
  description: string;
}

const navigationItems: NavigationItem[] = [
  {
    label: 'Dashboard',
    path: '/dashboard',
    icon: DashboardIcon,
    description: 'Overview and key metrics',
  },
  {
    label: 'Predictions',
    path: '/predictions',
    icon: PredictionsIcon,
    description: 'AI-powered match predictions',
  },
  {
    label: 'Betting Analysis',
    path: '/predictions/betting',
    icon: BettingIcon,
    description: 'ROI-optimized betting predictions',
  },
  {
    label: 'Matches',
    path: '/matches',
    icon: MatchesIcon,
    badge: 8,
    description: 'Browse and analyze matches',
  },
  {
    label: 'Teams',
    path: '/teams',
    icon: TeamsIcon,
    badge: 17,
    description: 'Team statistics and performance',
  },
  {
    label: 'Live Scores',
    path: '/live',
    icon: LiveIcon,
    badge: 0,
    description: 'Real-time match updates',
  },
];

const Sidebar: React.FC<SidebarProps> = ({ open, onClose }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleNavigation = (path: string) => {
    navigate(path);
    if (isMobile) {
      onClose();
    }
  };

  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Toolbar spacer */}
      <Toolbar />
      
      {/* Navigation Header */}
      <Box sx={{ p: 2, backgroundColor: theme.palette.grey[50] }}>
        <Typography variant="overline" color="textSecondary" fontWeight={600}>
          Navigation
        </Typography>
      </Box>

      {/* Main Navigation */}
      <List sx={{ flexGrow: 1, px: 1 }}>
        {navigationItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path || 
                          (item.path === '/dashboard' && location.pathname === '/');
          
          return (
            <ListItem key={item.path} disablePadding sx={{ mb: 0.5 }}>
              <ListItemButton
                onClick={() => handleNavigation(item.path)}
                sx={{
                  borderRadius: 2,
                  mx: 1,
                  backgroundColor: isActive ? theme.palette.primary.main : 'transparent',
                  color: isActive ? 'white' : 'inherit',
                  '&:hover': {
                    backgroundColor: isActive 
                      ? theme.palette.primary.dark 
                      : theme.palette.action.hover,
                  },
                  transition: 'all 0.2s ease',
                }}
              >
                <ListItemIcon
                  sx={{
                    color: isActive ? 'white' : theme.palette.primary.main,
                    minWidth: 40,
                  }}
                >
                  {item.badge !== undefined ? (
                    <Badge 
                      badgeContent={item.badge} 
                      color="error"
                      sx={{
                        '& .MuiBadge-badge': {
                          fontSize: '0.7rem',
                          height: 16,
                          minWidth: 16,
                        },
                      }}
                    >
                      <Icon />
                    </Badge>
                  ) : (
                    <Icon />
                  )}
                </ListItemIcon>
                <ListItemText 
                  primary={item.label}
                  secondary={!isActive ? item.description : undefined}
                  primaryTypographyProps={{
                    fontWeight: isActive ? 600 : 500,
                    fontSize: '0.95rem',
                  }}
                  secondaryTypographyProps={{
                    fontSize: '0.75rem',
                    color: isActive ? 'rgba(255,255,255,0.7)' : 'textSecondary',
                  }}
                />
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>

      <Divider />

      {/* Analytics Section */}
      <Box sx={{ p: 2 }}>
        <Typography variant="overline" color="textSecondary" fontWeight={600} sx={{ mb: 1, display: 'block' }}>
          Quick Stats
        </Typography>
        
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" color="textSecondary">
              Total Matches
            </Typography>
            <Typography variant="body2" fontWeight={600} color="primary">
              8
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" color="textSecondary">
              Teams
            </Typography>
            <Typography variant="body2" fontWeight={600} color="primary">
              17
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="body2" color="textSecondary">
              Venues
            </Typography>
            <Typography variant="body2" fontWeight={600} color="primary">
              16
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Footer */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Typography variant="caption" color="textSecondary" align="center" display="block">
          NRL Predictor Pro V5
        </Typography>
        <Typography variant="caption" color="textSecondary" align="center" display="block">
          Professional Sports Analytics
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Drawer
      variant={isMobile ? 'temporary' : 'persistent'}
      open={open}
      onClose={onClose}
      sx={{
        width: open ? 240 : 0, // Dynamic width based on open state
        flexShrink: 0,
        transition: 'width 0.3s cubic-bezier(0.4, 0, 0.2, 1)', // Smooth width transition
        '& .MuiDrawer-paper': {
          width: 240,
          boxSizing: 'border-box',
          borderRight: open ? `1px solid ${theme.palette.divider}` : 'none',
          // Ensure sidebar is positioned correctly
          position: 'fixed',
          top: 0,
          left: 0,
          height: '100vh',
          zIndex: theme.zIndex.drawer,
          // Remove any default margins or padding that could create gaps
          margin: 0,
          padding: 0,
          // Smooth transition for border
          transition: 'border-right 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        },
      }}
    >
      {drawerContent}
    </Drawer>
  );
};

export default Sidebar;
