import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { CssBaseline, Box, CircularProgress, Typography } from '@mui/material';
import RugbyProgressBar from './components/common/RugbyProgressBar';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Theme Context
import { ThemeProvider } from './contexts/ThemeContext';

// Components
import Header from './components/common/Header';
import Sidebar from './components/common/Sidebar';
import Footer from './components/common/Footer';

// Pages - Lazy load heavy prediction pages for better performance
import Dashboard from './pages/Dashboard';
import Matches from './pages/Matches';
import Teams from './pages/Teams';
import LiveScores from './pages/LiveScores';

// Lazy load prediction pages to improve initial load time
const Predictions = React.lazy(() => import('./pages/Predictions'));
const EnhancedPredictions = React.lazy(() => import('./pages/EnhancedPredictions'));
const BettingPredictions = React.lazy(() => import('./pages/BettingPredictions'));

// Import performance testing (only in development)
if (process.env.NODE_ENV === 'development') {
  import('./utils/performanceTest');
}

// Create React Query client with strict caching to prevent excessive API calls
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      retryDelay: 2000,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      staleTime: 5 * 60 * 1000, // 5 minutes - aggressive caching
      gcTime: 15 * 60 * 1000, // 15 minutes - keep in memory longer
      // Prevent duplicate requests
      refetchInterval: false,
      refetchIntervalInBackground: false,
    },
    mutations: {
      retry: 1,
      retryDelay: 1000,
    },
  },
});

function App() {
  const [sidebarOpen, setSidebarOpen] = React.useState(false);

  // Ensure loading screen is hidden when React app mounts
  React.useEffect(() => {
    const hideLoadingScreen = () => {
      document.body.classList.add('loaded');
      const loadingScreen = document.getElementById('loading-screen');
      if (loadingScreen) {
        loadingScreen.classList.add('hidden');
        loadingScreen.style.display = 'none';
      }
    };

    // Hide immediately if not already hidden
    setTimeout(hideLoadingScreen, 100);
  }, []);

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <CssBaseline />
        <Router>
          <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
            {/* Header */}
            <Header onMenuClick={handleSidebarToggle} />

            <Box sx={{ display: 'flex', flex: 1 }}>
              {/* Sidebar */}
              <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} />

              {/* Main Content */}
              <Box
                component="main"
                sx={{
                  flexGrow: 1,
                  p: 3,
                  pt: { xs: '72px', sm: '80px' }, // Account for fixed header height
                  width: {
                    xs: '100%', // Full width on mobile
                    sm: sidebarOpen ? `calc(100% - 240px)` : '100%' // Responsive width on desktop
                  },
                  ml: {
                    xs: 0, // No margin on mobile
                    sm: sidebarOpen ? '240px' : 0 // Conditional margin on desktop
                  },
                  transition: 'all 0.3s ease-in-out', // Smooth transition for both margin and width
                  minHeight: '100vh',
                  boxSizing: 'border-box',
                  overflow: 'hidden', // Prevent horizontal scroll
                }}
              >
                <React.Suspense fallback={
                  <Box sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    minHeight: '80vh',
                    flexDirection: 'column',
                    textAlign: 'center',
                    p: 3
                  }}>
                    <RugbyProgressBar
                      message="Loading prediction models..."
                      duration={4}
                      showPercentage={true}
                    />
                  </Box>
                }>
                  <Routes>
                    <Route path="/" element={<Dashboard />} />
                    <Route path="/dashboard" element={<Dashboard />} />
                    <Route path="/matches" element={<Matches />} />
                    <Route path="/teams" element={<Teams />} />
                    <Route path="/predictions" element={<EnhancedPredictions />} />
                    <Route path="/predictions/basic" element={<Predictions />} />
                    <Route path="/predictions/betting" element={<BettingPredictions />} />
                    <Route path="/live" element={<LiveScores />} />
                  </Routes>
                </React.Suspense>
              </Box>
            </Box>

            {/* Footer */}
            <Footer />
          </Box>
        </Router>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
