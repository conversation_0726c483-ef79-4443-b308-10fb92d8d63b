import React, { useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  LinearProgress,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
  useTheme,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  SportsFootball as MatchIcon,
  Groups as TeamsIcon,
  Psychology as PredictionIcon,
  LiveTv as LiveIcon,
  Analytics as AnalyticsIcon,
  EmojiEvents as TrophyIcon,
  Speed as SpeedIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { apiService, HealthResponse } from '../services/api';

// Import modern components
import SectionHeader from '../components/common/SectionHeader';
import StatusBadge from '../components/common/StatusBadge';
import ApiCard from '../components/common/ApiCard';
import CodeBlock from '../components/common/CodeBlock';
import GradientCard from '../components/common/GradientCard';
import AnimatedProgress from '../components/common/AnimatedProgress';
import PageWrapper from '../components/layout/PageWrapper';

// Import theme context
import { useTheme as useCustomTheme } from '../contexts/ThemeContext';

const Dashboard: React.FC = () => {
  const theme = useTheme();
  const { gradients } = useCustomTheme();

  // Fetch dashboard data with controlled loading to prevent excessive API calls
  const { data: healthData, error: healthError, isLoading: healthLoading } = useQuery<HealthResponse>({
    queryKey: ['health'],
    queryFn: apiService.healthCheck,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
  });

  const { data: matchesData, refetch: refetchMatches } = useQuery({
    queryKey: ['recent-matches'],
    queryFn: () => apiService.getRecentMatches({ limit: 5, completed_only: true, order: 'desc' }),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    enabled: false, // Disable automatic loading
  });

  const { data: teamsData, refetch: refetchTeams } = useQuery({
    queryKey: ['teams'],
    queryFn: apiService.getTeams,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    enabled: false, // Disable automatic loading
  });

  const { data: modelInfo, refetch: refetchModelInfo } = useQuery({
    queryKey: ['model-info'],
    queryFn: apiService.getModelInfo,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    enabled: false, // Disable automatic loading
  });



  // Modern gradient stats cards data
  const statsCards = [
    {
      title: 'Total Matches',
      value: healthData?.database?.total_matches || 0,
      icon: MatchIcon,
      gradient: gradients.primary,
      change: '+2 this week',
      changeType: 'positive' as const,
    },
    {
      title: 'Active Teams',
      value: healthData?.database?.teams_count || 0,
      icon: TeamsIcon,
      gradient: gradients.warning,
      change: 'All active',
      changeType: 'neutral' as const,
    },
    {
      title: 'Predictions Made',
      value: '156',
      icon: PredictionIcon,
      gradient: gradients.success,
      change: '+12 today',
      changeType: 'positive' as const,
    },
    {
      title: 'Accuracy Rate',
      value: '67.3%',
      icon: AnalyticsIcon,
      gradient: gradients.secondary,
      change: '+2.1% this month',
      changeType: 'positive' as const,
    },
  ];

  // Recent matches
  const recentMatches = matchesData?.matches?.slice(0, 3) || [];

  const handleRefresh = () => {
    // Refresh all queries
    window.location.reload();
  };

  const handleInfo = () => {
    // Show info modal or navigate to docs
    window.open('http://localhost:8000/docs', '_blank');
  };



  return (
    <PageWrapper>
      {/* FastAPI-style Header */}
      <SectionHeader
        title="NRL Predictor Pro V5"
        subtitle="Dashboard"
        description="Your comprehensive sports analytics platform with real-time predictions and advanced machine learning insights."
        badge={{
          label: "v5.0.0",
          color: "primary",
          variant: "filled"
        }}
        status={{
          label: healthData?.status === 'healthy' ? 'System Healthy' : 'System Issues',
          color: healthData?.status === 'healthy' ? 'success' : 'error'
        }}
        onRefresh={handleRefresh}
        onInfo={handleInfo}
      />

      {/* Manual Load Controls */}
      {(!matchesData || !teamsData || !modelInfo) && (
        <Card sx={{ mb: 3, bgcolor: 'background.paper' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Load Dashboard Data
            </Typography>
            <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
              Click the buttons below to load specific data sections:
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                size="small"
                onClick={() => refetchMatches()}
                disabled={!!matchesData}
              >
                {matchesData ? 'Matches Loaded' : 'Load Recent Matches'}
              </Button>
              <Button
                variant="contained"
                size="small"
                onClick={() => refetchTeams()}
                disabled={!!teamsData}
              >
                {teamsData ? 'Teams Loaded' : 'Load Teams Data'}
              </Button>
              <Button
                variant="contained"
                size="small"
                onClick={() => refetchModelInfo()}
                disabled={!!modelInfo}
              >
                {modelInfo ? 'Model Info Loaded' : 'Load Model Info'}
              </Button>
            </Box>
          </CardContent>
        </Card>
      )}

      {/* Modern Gradient Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {statsCards.map((card, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <GradientCard
              title={card.title}
              value={card.value}
              icon={card.icon}
              gradient={card.gradient}
              change={card.change}
              changeType={card.changeType}
              sx={{ height: '100%' }}
            />
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* Modern System Status */}
        <Grid item xs={12} md={6}>
          <Card sx={{
            background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)',
          }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <SpeedIcon color="primary" sx={{ mr: 1, fontSize: '1.5rem' }} />
                <Typography variant="h6" fontWeight={700}>
                  System Status
                </Typography>
                <StatusBadge status="success" label="All Systems Operational" sx={{ ml: 'auto' }} />
              </Box>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <AnimatedProgress
                  label="API Health"
                  value={100}
                  gradient={gradients.success}
                  icon={<Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: 'success.main' }} />}
                  subtitle="All endpoints responding normally"
                />

                <AnimatedProgress
                  label="Database Connection"
                  value={100}
                  gradient={gradients.primary}
                  icon={<Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: 'primary.main' }} />}
                  subtitle={`${healthData?.database?.total_matches || 0} matches available`}
                />

                <AnimatedProgress
                  label="Prediction Model"
                  value={85}
                  gradient={gradients.warning}
                  icon={<Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: 'warning.main' }} />}
                  subtitle="Model accuracy and performance"
                />
              </Box>

              <Box sx={{ mt: 3, pt: 2, borderTop: '1px solid rgba(0,0,0,0.1)' }}>
                <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 500 }}>
                  Last updated: {new Date().toLocaleTimeString()}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Matches */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <MatchIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6" fontWeight={600}>
                    Recent Matches
                  </Typography>
                </Box>
                <Button size="small" color="primary">
                  View All
                </Button>
              </Box>

              <List>
                {recentMatches.map((match: any, index: number) => (
                  <React.Fragment key={match.match_id || index}>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemAvatar>
                        <Avatar sx={{ backgroundColor: theme.palette.primary.main }}>
                          <MatchIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={`${match.home_team} vs ${match.away_team}`}
                        secondary={
                          <Box>
                            <Typography variant="body2" color="textSecondary">
                              Round {match.round}, {match.season} • {match.venue}
                            </Typography>
                            {match.home_score !== undefined && match.away_score !== undefined && (
                              <Typography variant="body2" fontWeight={600} color="primary">
                                {match.home_score} - {match.away_score}
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < recentMatches.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Model Information */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PredictionIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6" fontWeight={600}>
                  Prediction Model
                </Typography>
              </Box>

              {modelInfo && (
                <Box>
                  <Typography variant="body1" fontWeight={500} gutterBottom>
                    {modelInfo.model_name}
                  </Typography>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                    {modelInfo.description}
                  </Typography>
                  
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" fontWeight={500} gutterBottom>
                      Key Features:
                    </Typography>
                    {modelInfo.features?.slice(0, 3).map((feature: string, index: number) => (
                      <Chip
                        key={index}
                        label={feature}
                        size="small"
                        variant="outlined"
                        sx={{ mr: 1, mb: 1 }}
                      />
                    ))}
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="body2" color="textSecondary">
                      Accuracy: {modelInfo.accuracy_range}
                    </Typography>
                    <Chip label={`v${modelInfo.version}`} color="primary" size="small" />
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Modern API Endpoints */}
        <Grid item xs={12}>
          <Card sx={{
            background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)',
          }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Box sx={{
                  p: 1,
                  borderRadius: 2,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  mr: 2
                }}>
                  <AnalyticsIcon sx={{ color: 'white', fontSize: '1.5rem' }} />
                </Box>
                <Typography variant="h6" fontWeight={700}>
                  API Endpoints
                </Typography>
                <StatusBadge status="success" label="All Systems Operational" sx={{ ml: 'auto' }} />
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <ApiCard
                    method="GET"
                    endpoint="/api/matches"
                    title="Get Matches"
                    description="Retrieve NRL match data with filtering options"
                    parameters={[
                      { name: 'limit', type: 'integer', required: false, description: 'Number of matches to return' },
                      { name: 'team', type: 'string', required: false, description: 'Filter by team name' },
                      { name: 'season', type: 'integer', required: false, description: 'Filter by season year' }
                    ]}
                    responseExample={{
                      matches: [
                        {
                          match_id: 1,
                          home_team: "Brisbane Broncos",
                          away_team: "Sydney Roosters",
                          home_score: 24,
                          away_score: 18,
                          venue: "Suncorp Stadium",
                          round: 1,
                          season: 2024
                        }
                      ],
                      total: 1162,
                      page: 1
                    }}
                    onTry={() => window.open('http://localhost:8000/api/matches', '_blank')}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <ApiCard
                    method="POST"
                    endpoint="/api/predictions"
                    title="Make Prediction"
                    description="Generate match outcome predictions using ML models"
                    requestExample={{
                      home_team: "Brisbane Broncos",
                      away_team: "Sydney Roosters",
                      venue: "Suncorp Stadium",
                      round: 15,
                      season: 2024
                    }}
                    responseExample={{
                      prediction: {
                        home_win_probability: 0.67,
                        away_win_probability: 0.33,
                        predicted_score: {
                          home: 24,
                          away: 18
                        },
                        confidence: 0.85,
                        model_version: "v5.0.0"
                      }
                    }}
                    onTry={() => window.open('http://localhost:8000/docs#/predictions/make_prediction_api_predictions_post', '_blank')}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Modern Quick Actions */}
        <Grid item xs={12} md={6}>
          <Card sx={{
            background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)',
          }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Box sx={{
                  p: 1,
                  borderRadius: 2,
                  background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                  mr: 2
                }}>
                  <TrophyIcon sx={{ color: 'white', fontSize: '1.5rem' }} />
                </Box>
                <Typography variant="h6" fontWeight={700}>
                  Quick Actions
                </Typography>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<PredictionIcon />}
                    sx={{
                      py: 1.5,
                      background: gradients.primary,
                      '&:hover': {
                        background: gradients.primary,
                        filter: 'brightness(0.9)',
                      }
                    }}
                    onClick={() => window.location.href = '/predictions'}
                  >
                    New Prediction
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<LiveIcon />}
                    sx={{
                      py: 1.5,
                      background: gradients.success,
                      '&:hover': {
                        background: gradients.success,
                        filter: 'brightness(0.9)',
                      }
                    }}
                    onClick={() => window.location.href = '/live'}
                  >
                    Live Scores
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<AnalyticsIcon />}
                    sx={{
                      py: 1.5,
                      background: gradients.warning,
                      '&:hover': {
                        background: gradients.warning,
                        filter: 'brightness(0.9)',
                      }
                    }}
                    onClick={() => window.location.href = '/teams'}
                  >
                    Team Stats
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<MatchIcon />}
                    sx={{
                      py: 1.5,
                      background: gradients.secondary,
                      '&:hover': {
                        background: gradients.secondary,
                        filter: 'brightness(0.9)',
                      }
                    }}
                    onClick={() => window.location.href = '/matches'}
                  >
                    Browse Matches
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Modern Code Examples */}
        <Grid item xs={12} md={6}>
          <Card sx={{
            background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)',
          }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Box sx={{
                  p: 1,
                  borderRadius: 2,
                  background: 'linear-gradient(135deg, #00d4aa 0%, #4ecdc4 100%)',
                  mr: 2
                }}>
                  <LiveIcon sx={{ color: 'white', fontSize: '1.5rem' }} />
                </Box>
                <Typography variant="h6" fontWeight={700}>
                  Quick Start Examples
                </Typography>
              </Box>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Get started with the NRL Predictor API using these examples:
              </Typography>

              <CodeBlock
                title="Fetch Latest Matches"
                language="javascript"
                code={`// Fetch latest NRL matches
const response = await fetch('http://localhost:8000/api/matches?limit=5');
const data = await response.json();

console.log(\`Found \${data.total} matches\`);
data.matches.forEach(match => {
  console.log(\`\${match.home_team} vs \${match.away_team}: \${match.home_score}-\${match.away_score}\`);
});`}
              />

              <CodeBlock
                title="Make a Prediction"
                language="python"
                code={`import requests

# Make a match prediction
prediction_data = {
    "home_team": "Brisbane Broncos",
    "away_team": "Sydney Roosters",
    "venue": "Suncorp Stadium",
    "round": 15,
    "season": 2024
}

response = requests.post(
    "http://localhost:8000/api/predictions",
    json=prediction_data
)

result = response.json()
print(f"Home win probability: {result['prediction']['home_win_probability']:.2%}")
print(f"Predicted score: {result['prediction']['predicted_score']['home']}-{result['prediction']['predicted_score']['away']}")`}
              />
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </PageWrapper>
  );
};

export default Dashboard;
