{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Personal\\\\Sources\\\\nrl_predictor_v5\\\\frontend\\\\src\\\\components\\\\layout\\\\PageWrapper.tsx\";\nimport React from 'react';\nimport { Box } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PageWrapper = ({\n  children,\n  noPadding = false,\n  fullHeight = false,\n  sx,\n  ...props\n}) => {\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      // Dynamic padding that anchors to sidebar edge\n      p: noPadding ? 0 : 3,\n      pt: noPadding ? 0 : 2,\n      // Reduced top padding since header spacing is handled in App.tsx\n      width: '100%',\n      minHeight: fullHeight ? 'calc(100vh - 64px)' : 'auto',\n      // Ensure content starts right at the sidebar edge\n      margin: 0,\n      // Prevent any overflow\n      overflow: 'hidden',\n      // Smooth transitions when sidebar toggles\n      transition: 'padding 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n      // Ensure proper box sizing\n      boxSizing: 'border-box',\n      ...sx\n    },\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n};\n_c = PageWrapper;\nexport default PageWrapper;\nvar _c;\n$RefreshReg$(_c, \"PageWrapper\");", "map": {"version": 3, "names": ["React", "Box", "jsxDEV", "_jsxDEV", "PageWrapper", "children", "noPadding", "fullHeight", "sx", "props", "p", "pt", "width", "minHeight", "margin", "overflow", "transition", "boxSizing", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Personal/Sources/nrl_predictor_v5/frontend/src/components/layout/PageWrapper.tsx"], "sourcesContent": ["import React from 'react';\nimport { Box, BoxProps } from '@mui/material';\n\ninterface PageWrapperProps extends BoxProps {\n  children: React.ReactNode;\n  noPadding?: boolean;\n  fullHeight?: boolean;\n}\n\nconst PageWrapper: React.FC<PageWrapperProps> = ({ \n  children, \n  noPadding = false, \n  fullHeight = false,\n  sx,\n  ...props \n}) => {\n  return (\n    <Box\n      sx={{\n        // Dynamic padding that anchors to sidebar edge\n        p: noPadding ? 0 : 3,\n        pt: noPadding ? 0 : 2, // Reduced top padding since header spacing is handled in App.tsx\n        width: '100%',\n        minHeight: fullHeight ? 'calc(100vh - 64px)' : 'auto',\n        // Ensure content starts right at the sidebar edge\n        margin: 0,\n        // Prevent any overflow\n        overflow: 'hidden',\n        // Smooth transitions when sidebar toggles\n        transition: 'padding 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n        // Ensure proper box sizing\n        boxSizing: 'border-box',\n        ...sx,\n      }}\n      {...props}\n    >\n      {children}\n    </Box>\n  );\n};\n\nexport default PageWrapper;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,QAAkB,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ9C,MAAMC,WAAuC,GAAGA,CAAC;EAC/CC,QAAQ;EACRC,SAAS,GAAG,KAAK;EACjBC,UAAU,GAAG,KAAK;EAClBC,EAAE;EACF,GAAGC;AACL,CAAC,KAAK;EACJ,oBACEN,OAAA,CAACF,GAAG;IACFO,EAAE,EAAE;MACF;MACAE,CAAC,EAAEJ,SAAS,GAAG,CAAC,GAAG,CAAC;MACpBK,EAAE,EAAEL,SAAS,GAAG,CAAC,GAAG,CAAC;MAAE;MACvBM,KAAK,EAAE,MAAM;MACbC,SAAS,EAAEN,UAAU,GAAG,oBAAoB,GAAG,MAAM;MACrD;MACAO,MAAM,EAAE,CAAC;MACT;MACAC,QAAQ,EAAE,QAAQ;MAClB;MACAC,UAAU,EAAE,2CAA2C;MACvD;MACAC,SAAS,EAAE,YAAY;MACvB,GAAGT;IACL,CAAE;IAAA,GACEC,KAAK;IAAAJ,QAAA,EAERA;EAAQ;IAAAa,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACC,EAAA,GA9BIlB,WAAuC;AAgC7C,eAAeA,WAAW;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}